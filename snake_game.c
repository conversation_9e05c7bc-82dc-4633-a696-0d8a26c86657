/**
 * @file snake_game.c
 * @brief 贪吃蛇游戏实现文件
 * <AUTHOR> Assistant
 * @date 2024
 */

#include "snake_game.h"
#include <stdlib.h>

/* 全局游戏变量 */
static Game_t game;
static uint32_t system_tick = 0;

/**
 * @brief 游戏初始化
 */
void Game_Init(void)
{
    /* 初始化LCD */
    LCD_Init();

    /* 初始化按键 */
    Game_KeyInit();

    /* 重置游戏 */
    Game_Reset();

    /* 绘制初始界面 */
    Game_Draw();
}

/**
 * @brief 按键初始化
 */
void Game_KeyInit(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    /* 使能GPIO时钟 */
    __HAL_RCC_GPIOA_CLK_ENABLE();

    /* 配置按键引脚为输入模式，上拉 */
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;

    /* 配置上下左右按键 */
    GPIO_InitStruct.Pin = KEY_UP_PIN;
    HAL_GPIO_Init(KEY_UP_PORT, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = KEY_DOWN_PIN;
    HAL_GPIO_Init(KEY_DOWN_PORT, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = KEY_LEFT_PIN;
    HAL_GPIO_Init(KEY_LEFT_PORT, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = KEY_RIGHT_PIN;
    HAL_GPIO_Init(KEY_RIGHT_PORT, &GPIO_InitStruct);
}

/**
 * @brief 重置游戏
 */
void Game_Reset(void)
{
    uint8_t i;

    /* 初始化蛇 */
    game.snake.length = SNAKE_INIT_LENGTH;
    game.snake.direction = DIR_RIGHT;
    game.snake.next_direction = DIR_RIGHT;

    /* 设置蛇的初始位置 */
    for(i = 0; i < SNAKE_INIT_LENGTH; i++)
    {
        game.snake.body[i].x = (SNAKE_INIT_X / SNAKE_BLOCK_SIZE) - i;
        game.snake.body[i].y = SNAKE_INIT_Y / SNAKE_BLOCK_SIZE;
    }

    /* 初始化食物 */
    game.food.exists = false;
    Game_GenerateFood();

    /* 初始化游戏状态 */
    game.state = GAME_RUNNING;
    game.score = 0;
    game.level = 1;
    game.speed = INITIAL_SPEED;
    game.last_move_time = Game_GetSystemTime();
}

/**
 * @brief 游戏主循环更新
 */
void Game_Update(void)
{
    uint32_t current_time = Game_GetSystemTime();

    /* 处理输入 */
    Game_HandleInput();

    /* 检查是否到了移动时间 */
    if(game.state == GAME_RUNNING && (current_time - game.last_move_time) >= game.speed)
    {
        /* 移动蛇 */
        Game_MoveSnake();

        /* 检查碰撞 */
        if(Game_CheckCollision())
        {
            game.state = GAME_OVER;
        }

        /* 检查是否吃到食物 */
        if(Game_CheckFoodCollision())
        {
            Game_IncreaseScore();
            Game_GenerateFood();
        }

        game.last_move_time = current_time;

        /* 重绘游戏 */
        Game_Draw();
    }
}

/**
 * @brief 绘制游戏画面
 */
void Game_Draw(void)
{
    /* 清屏 */
    LCD_FillScreen(BACKGROUND_COLOR);

    /* 绘制游戏边框 */
    LCD_DrawRectangle(GAME_AREA_X - 1, GAME_AREA_Y - 1,
                     GAME_AREA_X + GAME_AREA_WIDTH, GAME_AREA_Y + GAME_AREA_HEIGHT,
                     BORDER_COLOR);

    /* 绘制蛇 */
    Game_DrawSnake();

    /* 绘制食物 */
    Game_DrawFood();

    /* 绘制UI */
    Game_DrawUI();

    /* 如果游戏结束，绘制游戏结束画面 */
    if(game.state == GAME_OVER)
    {
        Game_DrawGameOver();
    }
}

/**
 * @brief 绘制蛇
 */
void Game_DrawSnake(void)
{
    uint16_t i;

    for(i = 0; i < game.snake.length; i++)
    {
        if(i == 0)
        {
            /* 蛇头用不同颜色 */
            Game_DrawBlock(game.snake.body[i].x, game.snake.body[i].y, SNAKE_HEAD_COLOR);
        }
        else
        {
            /* 蛇身 */
            Game_DrawBlock(game.snake.body[i].x, game.snake.body[i].y, SNAKE_BODY_COLOR);
        }
    }
}

/**
 * @brief 绘制食物
 */
void Game_DrawFood(void)
{
    if(game.food.exists)
    {
        Game_DrawBlock(game.food.position.x, game.food.position.y, FOOD_COLOR);
    }
}

/**
 * @brief 绘制UI信息
 */
void Game_DrawUI(void)
{
    /* 显示得分 */
    LCD_ShowString(8, 2, "Score:", TEXT_COLOR, BACKGROUND_COLOR, 1);
    LCD_ShowNumber(50, 2, game.score, 4, TEXT_COLOR, BACKGROUND_COLOR, 1);

    /* 显示等级 */
    LCD_ShowString(8, 145, "Level:", TEXT_COLOR, BACKGROUND_COLOR, 1);
    LCD_ShowNumber(50, 145, game.level, 2, TEXT_COLOR, BACKGROUND_COLOR, 1);
}

/**
 * @brief 绘制游戏结束画面
 */
void Game_DrawGameOver(void)
{
    /* 绘制半透明背景 */
    LCD_FillRectangle(20, 60, 108, 100, COLOR_DARK_GRAY);
    LCD_DrawRectangle(20, 60, 108, 100, TEXT_COLOR);

    /* 显示游戏结束文字 */
    LCD_ShowString(30, 70, "GAME OVER", COLOR_RED, COLOR_DARK_GRAY, 1);
    LCD_ShowString(25, 85, "Press any key", TEXT_COLOR, COLOR_DARK_GRAY, 1);
}

/**
 * @brief 处理用户输入
 */
void Game_HandleInput(void)
{
    uint8_t keys = Game_ReadKeys();

    if(game.state == GAME_OVER)
    {
        /* 游戏结束状态，任意按键重新开始 */
        if(keys != 0)
        {
            Game_Reset();
        }
        return;
    }

    /* 根据按键改变蛇的方向 */
    if(keys & KEY_UP && game.snake.direction != DIR_DOWN)
    {
        game.snake.next_direction = DIR_UP;
    }
    else if(keys & KEY_DOWN && game.snake.direction != DIR_UP)
    {
        game.snake.next_direction = DIR_DOWN;
    }
    else if(keys & KEY_LEFT && game.snake.direction != DIR_RIGHT)
    {
        game.snake.next_direction = DIR_LEFT;
    }
    else if(keys & KEY_RIGHT && game.snake.direction != DIR_LEFT)
    {
        game.snake.next_direction = DIR_RIGHT;
    }
}

/**
 * @brief 检查碰撞
 * @return true: 发生碰撞, false: 无碰撞
 */
bool Game_CheckCollision(void)
{
    uint16_t i;
    Point_t head = game.snake.body[0];

    /* 检查是否撞墙 */
    if(head.x >= GRID_WIDTH || head.y >= GRID_HEIGHT)
    {
        return true;
    }

    /* 检查是否撞到自己 */
    for(i = 1; i < game.snake.length; i++)
    {
        if(head.x == game.snake.body[i].x && head.y == game.snake.body[i].y)
        {
            return true;
        }
    }

    return false;
}

/**
 * @brief 移动蛇
 */
void Game_MoveSnake(void)
{
    uint16_t i;
    Point_t new_head = game.snake.body[0];

    /* 更新方向 */
    game.snake.direction = game.snake.next_direction;

    /* 根据方向计算新的头部位置 */
    switch(game.snake.direction)
    {
        case DIR_UP:
            if(new_head.y > 0) new_head.y--;
            else new_head.y = GRID_HEIGHT - 1;  // 穿墙
            break;
        case DIR_DOWN:
            if(new_head.y < GRID_HEIGHT - 1) new_head.y++;
            else new_head.y = 0;  // 穿墙
            break;
        case DIR_LEFT:
            if(new_head.x > 0) new_head.x--;
            else new_head.x = GRID_WIDTH - 1;  // 穿墙
            break;
        case DIR_RIGHT:
            if(new_head.x < GRID_WIDTH - 1) new_head.x++;
            else new_head.x = 0;  // 穿墙
            break;
    }

    /* 移动蛇身 */
    for(i = game.snake.length - 1; i > 0; i--)
    {
        game.snake.body[i] = game.snake.body[i - 1];
    }

    /* 设置新的头部位置 */
    game.snake.body[0] = new_head;
}

/**
 * @brief 生成食物
 */
void Game_GenerateFood(void)
{
    uint8_t x, y;
    uint16_t i;
    bool valid_position;

    do {
        /* 随机生成食物位置 */
        x = rand() % GRID_WIDTH;
        y = rand() % GRID_HEIGHT;

        /* 检查是否与蛇身重叠 */
        valid_position = true;
        for(i = 0; i < game.snake.length; i++)
        {
            if(game.snake.body[i].x == x && game.snake.body[i].y == y)
            {
                valid_position = false;
                break;
            }
        }
    } while(!valid_position);

    game.food.position.x = x;
    game.food.position.y = y;
    game.food.exists = true;
}

/**
 * @brief 检查是否吃到食物
 * @return true: 吃到食物, false: 没有吃到
 */
bool Game_CheckFoodCollision(void)
{
    if(game.food.exists &&
       game.snake.body[0].x == game.food.position.x &&
       game.snake.body[0].y == game.food.position.y)
    {
        /* 增加蛇的长度 */
        if(game.snake.length < SNAKE_MAX_LENGTH)
        {
            game.snake.body[game.snake.length] = game.snake.body[game.snake.length - 1];
            game.snake.length++;
        }

        game.food.exists = false;
        return true;
    }

    return false;
}

/**
 * @brief 增加得分
 */
void Game_IncreaseScore(void)
{
    game.score += SCORE_PER_FOOD;

    /* 检查是否升级 */
    if(game.score >= game.level * LEVEL_UP_SCORE)
    {
        game.level++;
        Game_UpdateSpeed();
    }
}

/**
 * @brief 更新游戏速度
 */
void Game_UpdateSpeed(void)
{
    game.speed = INITIAL_SPEED - (game.level - 1) * SPEED_INCREMENT;
    if(game.speed < MIN_SPEED)
    {
        game.speed = MIN_SPEED;
    }
}

/**
 * @brief 获取系统时间 (简单的毫秒计数器)
 * @return 系统时间 (ms)
 */
uint32_t Game_GetSystemTime(void)
{
    return HAL_GetTick();
}

/**
 * @brief 读取按键状态
 * @return 按键状态位掩码
 */
uint8_t Game_ReadKeys(void)
{
    uint8_t keys = 0;

    /* 读取按键状态 (按键按下时为低电平) */
    if(HAL_GPIO_ReadPin(KEY_UP_PORT, KEY_UP_PIN) == GPIO_PIN_RESET)
        keys |= KEY_UP;
    if(HAL_GPIO_ReadPin(KEY_DOWN_PORT, KEY_DOWN_PIN) == GPIO_PIN_RESET)
        keys |= KEY_DOWN;
    if(HAL_GPIO_ReadPin(KEY_LEFT_PORT, KEY_LEFT_PIN) == GPIO_PIN_RESET)
        keys |= KEY_LEFT;
    if(HAL_GPIO_ReadPin(KEY_RIGHT_PORT, KEY_RIGHT_PIN) == GPIO_PIN_RESET)
        keys |= KEY_RIGHT;

    return keys;
}

/**
 * @brief 在网格位置绘制方块
 * @param grid_x 网格X坐标
 * @param grid_y 网格Y坐标
 * @param color 方块颜色
 */
void Game_DrawBlock(uint8_t grid_x, uint8_t grid_y, uint16_t color)
{
    uint16_t x = GAME_AREA_X + grid_x * SNAKE_BLOCK_SIZE;
    uint16_t y = GAME_AREA_Y + grid_y * SNAKE_BLOCK_SIZE;

    LCD_FillRectangle(x, y, x + SNAKE_BLOCK_SIZE - 1, y + SNAKE_BLOCK_SIZE - 1, color);
}

/**
 * @brief 清除网格位置的方块
 * @param grid_x 网格X坐标
 * @param grid_y 网格Y坐标
 */
void Game_ClearBlock(uint8_t grid_x, uint8_t grid_y)
{
    Game_DrawBlock(grid_x, grid_y, BACKGROUND_COLOR);
}
