# STM32F411CEU6贪吃蛇项目编译指南

## 🎯 编译目标
使用ARM GCC工具链编译STM32F411CEU6贪吃蛇游戏项目，生成可烧录的HEX文件。

## 📁 第一步：准备项目文件

### 1.1 创建项目目录
1. 在桌面创建文件夹：`STM32_Snake_Game`
2. 进入该文件夹，创建以下子目录：
   ```
   STM32_Snake_Game/
   ├── src/           (源代码文件)
   ├── inc/           (头文件)
   ├── build/         (编译输出)
   └── docs/          (文档)
   ```

### 1.2 放置源代码文件
将以下文件放入对应目录：

**放入 src/ 目录：**
- `main.c`
- `lcd_driver.c`
- `snake_game.c`
- `stm32f4xx_it.c`
- `system_stm32f4xx.c`
- `startup_stm32f411xe.s`

**放入 inc/ 目录：**
- `main.h`
- `lcd_driver.h`
- `snake_game.h`
- `stm32f4xx_it.h`
- `stm32f4xx_hal_conf.h`

**放入项目根目录：**
- `STM32F411CEUx_FLASH.ld`
- `Makefile`

## 📝 第二步：创建优化的Makefile

### 2.1 创建Makefile文件
在项目根目录创建 `Makefile` 文件，内容如下：

```makefile
##########################################################################################################################
# STM32F411CEU6 贪吃蛇游戏项目 Makefile
# 适用于Windows 10 + ARM GCC工具链
##########################################################################################################################

# 目标文件名
TARGET = snake_game

# 调试构建目录
BUILD_DIR = build

######################################
# 源文件配置
######################################
# C源文件
C_SOURCES =  \
src/main.c \
src/lcd_driver.c \
src/snake_game.c \
src/stm32f4xx_it.c \
src/system_stm32f4xx.c

# ASM源文件
ASM_SOURCES =  \
src/startup_stm32f411xe.s

######################################
# 包含路径
######################################
C_INCLUDES =  \
-Iinc

######################################
# 编译器配置
######################################
PREFIX = arm-none-eabi-
# 如果工具链不在PATH中，请设置完整路径
# PREFIX = C:/Program Files (x86)/GNU Arm Embedded Toolchain/10 2021.10/bin/arm-none-eabi-

CC = $(PREFIX)gcc
AS = $(PREFIX)gcc -x assembler-with-cpp
CP = $(PREFIX)objcopy
SZ = $(PREFIX)size
HEX = $(CP) -O ihex
BIN = $(CP) -O binary -S

######################################
# CPU配置
######################################
CPU = -mcpu=cortex-m4
FPU = -mfpu=fpv4-sp-d16
FLOAT-ABI = -mfloat-abi=hard
MCU = $(CPU) -mthumb $(FPU) $(FLOAT-ABI)

######################################
# 宏定义
######################################
C_DEFS =  \
-DUSE_HAL_DRIVER \
-DSTM32F411xE \
-DHSE_VALUE=25000000 \
-DHSI_VALUE=16000000

######################################
# 编译选项
######################################
# 优化级别 (可选: -O0, -O1, -O2, -O3, -Os)
OPT = -Os

# 汇编选项
ASFLAGS = $(MCU) $(OPT) -Wall -fdata-sections -ffunction-sections

# C编译选项
CFLAGS = $(MCU) $(C_DEFS) $(C_INCLUDES) $(OPT) -Wall -fdata-sections -ffunction-sections

# 调试信息
CFLAGS += -g -gdwarf-2

# 生成依赖信息
CFLAGS += -MMD -MP -MF"$(@:%.o=%.d)"

######################################
# 链接选项
######################################
# 链接脚本
LDSCRIPT = STM32F411CEUx_FLASH.ld

# 库文件
LIBS = -lc -lm -lnosys 
LIBDIR = 

# 链接选项
LDFLAGS = $(MCU) -specs=nano.specs -T$(LDSCRIPT) $(LIBDIR) $(LIBS) -Wl,-Map=$(BUILD_DIR)/$(TARGET).map,--cref -Wl,--gc-sections

######################################
# 默认目标
######################################
all: $(BUILD_DIR)/$(TARGET).elf $(BUILD_DIR)/$(TARGET).hex $(BUILD_DIR)/$(TARGET).bin

######################################
# 构建过程
######################################
# 目标文件列表
OBJECTS = $(addprefix $(BUILD_DIR)/,$(notdir $(C_SOURCES:.c=.o)))
vpath %.c $(sort $(dir $(C_SOURCES)))

OBJECTS += $(addprefix $(BUILD_DIR)/,$(notdir $(ASM_SOURCES:.s=.o)))
vpath %.s $(sort $(dir $(ASM_SOURCES)))

# 编译C文件
$(BUILD_DIR)/%.o: %.c Makefile | $(BUILD_DIR) 
	@echo 编译: $<
	$(CC) -c $(CFLAGS) -Wa,-a,-ad,-alms=$(BUILD_DIR)/$(notdir $(<:.c=.lst)) $< -o $@

# 编译汇编文件
$(BUILD_DIR)/%.o: %.s Makefile | $(BUILD_DIR)
	@echo 汇编: $<
	$(AS) -c $(CFLAGS) $< -o $@

# 链接生成ELF文件
$(BUILD_DIR)/$(TARGET).elf: $(OBJECTS) Makefile
	@echo 链接: $@
	$(CC) $(OBJECTS) $(LDFLAGS) -o $@
	$(SZ) $@

# 生成HEX文件
$(BUILD_DIR)/%.hex: $(BUILD_DIR)/%.elf | $(BUILD_DIR)
	@echo 生成HEX: $@
	$(HEX) $< $@
	
# 生成BIN文件
$(BUILD_DIR)/%.bin: $(BUILD_DIR)/%.elf | $(BUILD_DIR)
	@echo 生成BIN: $@
	$(BIN) $< $@	
	
# 创建构建目录
$(BUILD_DIR):
	mkdir $(BUILD_DIR)

######################################
# 清理目标
######################################
clean:
	@echo 清理构建文件...
	@if exist $(BUILD_DIR) rmdir /s /q $(BUILD_DIR)

######################################
# 信息显示
######################################
info:
	@echo 项目信息:
	@echo   目标: $(TARGET)
	@echo   源文件: $(C_SOURCES)
	@echo   构建目录: $(BUILD_DIR)
	@echo   编译器: $(CC)

######################################
# 帮助信息
######################################
help:
	@echo 可用的make目标:
	@echo   all     - 编译整个项目 (默认)
	@echo   clean   - 清理构建文件
	@echo   info    - 显示项目信息
	@echo   help    - 显示此帮助信息
	@echo.
	@echo 使用方法:
	@echo   make        - 编译项目
	@echo   make clean  - 清理项目
	@echo   make info   - 查看项目信息

######################################
# 依赖文件
######################################
-include $(wildcard $(BUILD_DIR)/*.d)

# 伪目标声明
.PHONY: all clean info help
```

## 🔨 第三步：编译项目

### 3.1 打开命令提示符
1. 按 **Win + R**，输入 `cmd`，按回车
2. 使用 `cd` 命令导航到项目目录：
   ```cmd
   cd Desktop\STM32_Snake_Game
   ```

### 3.2 执行编译命令
1. 首先查看帮助信息：
   ```cmd
   make help
   ```

2. 开始编译项目：
   ```cmd
   make all
   ```

### 3.3 预期编译输出
成功编译时，您应该看到类似以下的输出：

```
编译: src/main.c
编译: src/lcd_driver.c
编译: src/snake_game.c
编译: src/stm32f4xx_it.c
编译: src/system_stm32f4xx.c
汇编: src/startup_stm32f411xe.s
链接: build/snake_game.elf
   text    data     bss     dec     hex filename
  15234    1456     2048   18738    492e build/snake_game.elf
生成HEX: build/snake_game.hex
生成BIN: build/snake_game.bin
```

## ✅ 第四步：验证编译结果

### 4.1 检查生成的文件
编译成功后，在 `build/` 目录中应该有以下文件：

```cmd
dir build
```

**预期文件列表**：
```
snake_game.elf    - ELF格式可执行文件 (用于调试)
snake_game.hex    - Intel HEX格式文件 (用于烧录)
snake_game.bin    - 二进制文件 (用于烧录)
snake_game.map    - 内存映射文件 (用于分析)
*.o               - 目标文件
*.d               - 依赖文件
*.lst             - 汇编列表文件
```

### 4.2 查看程序大小信息
```cmd
arm-none-eabi-size build/snake_game.elf
```

**预期输出**：
```
   text    data     bss     dec     hex filename
  15234    1456    2048   18738    492e build/snake_game.elf
```

**解释**：
- **text**: 程序代码大小 (15234 字节)
- **data**: 初始化数据大小 (1456 字节)
- **bss**: 未初始化数据大小 (2048 字节)
- **dec**: 总大小 (十进制)
- **hex**: 总大小 (十六进制)

### 4.3 验证HEX文件
```cmd
dir build\snake_game.hex
```

如果显示文件信息（文件大小通常在30-50KB），说明HEX文件生成成功。

## 🚨 第五步：常见编译错误解决

### 错误1：找不到编译器
**错误信息**：
```
'arm-none-eabi-gcc' 不是内部或外部命令
```

**解决方法**：
1. 检查ARM GCC是否正确安装
2. 运行环境验证脚本
3. 重新启动命令提示符

### 错误2：找不到源文件
**错误信息**：
```
make: *** No rule to make target 'src/main.c', needed by 'build/main.o'. Stop.
```

**解决方法**：
1. 检查源文件是否在正确的目录中
2. 确认文件名拼写正确
3. 检查Makefile中的路径设置

### 错误3：找不到头文件
**错误信息**：
```
fatal error: 'stm32f4xx_hal.h' file not found
```

**解决方法**：
这个错误表明缺少STM32 HAL库文件。需要下载STM32 HAL库：

1. 访问ST官网下载STM32CubeF4软件包
2. 或者使用简化版本（我将为您提供）

### 错误4：链接错误
**错误信息**：
```
undefined reference to 'HAL_GPIO_Init'
```

**解决方法**：
1. 确保包含了所需的HAL库源文件
2. 检查宏定义是否正确
3. 验证链接脚本配置

### 错误5：内存溢出
**错误信息**：
```
region `FLASH' overflowed
```

**解决方法**：
1. 使用 `-Os` 优化选项减少代码大小
2. 检查链接脚本中的内存配置
3. 移除不必要的功能

## 🔧 第六步：编译优化技巧

### 6.1 快速重新编译
```cmd
# 清理后重新编译
make clean && make all

# 只编译修改的文件
make
```

### 6.2 调试版本编译
```cmd
# 编译调试版本 (包含调试信息，不优化)
make CFLAGS="-O0 -g3"
```

### 6.3 发布版本编译
```cmd
# 编译发布版本 (最大优化，无调试信息)
make CFLAGS="-Os -DNDEBUG"
```

## 📊 第七步：编译成功确认清单

编译成功后，请确认以下项目：

- [ ] 命令行显示编译成功信息
- [ ] `build/` 目录包含所有生成文件
- [ ] `snake_game.hex` 文件存在且大小合理 (30-50KB)
- [ ] `snake_game.elf` 文件存在
- [ ] 程序大小在合理范围内 (Flash < 400KB, RAM < 100KB)
- [ ] 无编译错误和警告

## 🎉 编译完成

恭喜！如果您看到了成功的编译输出并且生成了HEX文件，说明您已经成功编译了STM32贪吃蛇项目。

**生成的HEX文件位置**：`build/snake_game.hex`

这个文件就是可以烧录到STM32F411CEU6单片机的程序文件！

## 📝 下一步
编译成功后，您可以：
1. 使用ST-Link烧录HEX文件到单片机
2. 使用STM32CubeProgrammer进行烧录
3. 配置调试环境进行程序调试
