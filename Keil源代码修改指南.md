# STM32F411CEU6贪吃蛇游戏 - Keil源代码修改指南

## 🎯 修改目的
为了确保源代码能在Keil uVision5环境中正确编译，需要对部分文件进行适配修改。

## 📝 必需的修改

### 修改1：简化stm32f4xx_hal_conf.h文件

**文件位置**: `STM32_Snake_Game\Include\stm32f4xx_hal_conf.h`

**操作步骤**:
1. 用记事本打开该文件
2. **删除所有内容**
3. **复制粘贴**以下简化版本内容:

```c
/**
 * @file stm32f4xx_hal_conf.h
 * @brief HAL配置文件 - Keil简化版本
 */

#ifndef __STM32F4xx_HAL_CONF_H
#define __STM32F4xx_HAL_CONF_H

#ifdef __cplusplus
extern "C" {
#endif

/* 基本类型定义 */
#include <stdint.h>
#include <stddef.h>

/* STM32F411xE芯片定义 */
#if !defined(STM32F411xE)
#define STM32F411xE
#endif

/* 基本寄存器地址定义 */
#define PERIPH_BASE           0x40000000UL
#define AHB1PERIPH_BASE       (PERIPH_BASE + 0x00020000UL)
#define GPIOA_BASE            (AHB1PERIPH_BASE + 0x0000UL)
#define GPIOB_BASE            (AHB1PERIPH_BASE + 0x0400UL)
#define RCC_BASE              (AHB1PERIPH_BASE + 0x3800UL)

/* GPIO寄存器结构体 */
typedef struct
{
    volatile uint32_t MODER;    /* GPIO端口模式寄存器 */
    volatile uint32_t OTYPER;   /* GPIO端口输出类型寄存器 */
    volatile uint32_t OSPEEDR;  /* GPIO端口输出速度寄存器 */
    volatile uint32_t PUPDR;    /* GPIO端口上拉/下拉寄存器 */
    volatile uint32_t IDR;      /* GPIO端口输入数据寄存器 */
    volatile uint32_t ODR;      /* GPIO端口输出数据寄存器 */
    volatile uint32_t BSRR;     /* GPIO端口位设置/复位寄存器 */
    volatile uint32_t LCKR;     /* GPIO端口配置锁定寄存器 */
    volatile uint32_t AFR[2];   /* GPIO备用功能寄存器 */
} GPIO_TypeDef;

/* RCC寄存器结构体 */
typedef struct
{
    volatile uint32_t CR;            /* RCC时钟控制寄存器 */
    volatile uint32_t PLLCFGR;       /* RCC PLL配置寄存器 */
    volatile uint32_t CFGR;          /* RCC时钟配置寄存器 */
    volatile uint32_t CIR;           /* RCC时钟中断寄存器 */
    volatile uint32_t AHB1RSTR;      /* RCC AHB1外设复位寄存器 */
    volatile uint32_t AHB2RSTR;      /* RCC AHB2外设复位寄存器 */
    volatile uint32_t AHB3RSTR;      /* RCC AHB3外设复位寄存器 */
    uint32_t      RESERVED0;         /* 保留 */
    volatile uint32_t APB1RSTR;      /* RCC APB1外设复位寄存器 */
    volatile uint32_t APB2RSTR;      /* RCC APB2外设复位寄存器 */
    uint32_t      RESERVED1[2];      /* 保留 */
    volatile uint32_t AHB1ENR;       /* RCC AHB1外设时钟使能寄存器 */
    volatile uint32_t AHB2ENR;       /* RCC AHB2外设时钟使能寄存器 */
    volatile uint32_t AHB3ENR;       /* RCC AHB3外设时钟使能寄存器 */
    uint32_t      RESERVED2;         /* 保留 */
    volatile uint32_t APB1ENR;       /* RCC APB1外设时钟使能寄存器 */
    volatile uint32_t APB2ENR;       /* RCC APB2外设时钟使能寄存器 */
} RCC_TypeDef;

/* 外设指针定义 */
#define GPIOA               ((GPIO_TypeDef *) GPIOA_BASE)
#define GPIOB               ((GPIO_TypeDef *) GPIOB_BASE)
#define RCC                 ((RCC_TypeDef *) RCC_BASE)

/* GPIO引脚定义 */
#define GPIO_PIN_0          0x0001U
#define GPIO_PIN_1          0x0002U
#define GPIO_PIN_2          0x0004U
#define GPIO_PIN_3          0x0008U
#define GPIO_PIN_4          0x0010U
#define GPIO_PIN_5          0x0020U
#define GPIO_PIN_13         0x2000U
#define GPIO_PIN_15         0x8000U

/* GPIO模式定义 */
#define GPIO_MODE_INPUT     0x00000000U
#define GPIO_MODE_OUTPUT_PP 0x00000001U
#define GPIO_MODE_OUTPUT_OD 0x00000011U
#define GPIO_MODE_AF_PP     0x00000002U

/* GPIO速度定义 */
#define GPIO_SPEED_FREQ_LOW    0x00000000U
#define GPIO_SPEED_FREQ_MEDIUM 0x00000001U
#define GPIO_SPEED_FREQ_HIGH   0x00000002U
#define GPIO_SPEED_FREQ_VERY_HIGH 0x00000003U

/* GPIO上拉下拉定义 */
#define GPIO_NOPULL         0x00000000U
#define GPIO_PULLUP         0x00000001U
#define GPIO_PULLDOWN       0x00000002U

/* GPIO状态定义 */
typedef enum
{
    GPIO_PIN_RESET = 0,
    GPIO_PIN_SET
} GPIO_PinState;

/* GPIO初始化结构体 */
typedef struct
{
    uint32_t Pin;       /* GPIO引脚 */
    uint32_t Mode;      /* GPIO模式 */
    uint32_t Pull;      /* GPIO上拉下拉 */
    uint32_t Speed;     /* GPIO速度 */
} GPIO_InitTypeDef;

/* RCC时钟使能位定义 */
#define RCC_AHB1ENR_GPIOAEN 0x00000001U
#define RCC_AHB1ENR_GPIOBEN 0x00000002U

/* 系统滴答定时器 */
extern volatile uint32_t uwTick;

/* 函数声明 */
void HAL_GPIO_Init(GPIO_TypeDef *GPIOx, GPIO_InitTypeDef *GPIO_Init);
void HAL_GPIO_WritePin(GPIO_TypeDef *GPIOx, uint16_t GPIO_Pin, GPIO_PinState PinState);
GPIO_PinState HAL_GPIO_ReadPin(GPIO_TypeDef *GPIOx, uint16_t GPIO_Pin);
uint32_t HAL_GetTick(void);
void HAL_Delay(uint32_t Delay);
void HAL_IncTick(void);

/* 时钟使能宏 */
#define __HAL_RCC_GPIOA_CLK_ENABLE()   do { \
                                        uint32_t tmpreg = 0x00U; \
                                        SET_BIT(RCC->AHB1ENR, RCC_AHB1ENR_GPIOAEN);\
                                        tmpreg = READ_BIT(RCC->AHB1ENR, RCC_AHB1ENR_GPIOAEN);\
                                        UNUSED(tmpreg); \
                                      } while(0U)

#define __HAL_RCC_GPIOB_CLK_ENABLE()   do { \
                                        uint32_t tmpreg = 0x00U; \
                                        SET_BIT(RCC->AHB1ENR, RCC_AHB1ENR_GPIOBEN);\
                                        tmpreg = read_BIT(RCC->AHB1ENR, RCC_AHB1ENR_GPIOBEN);\
                                        UNUSED(tmpreg); \
                                      } while(0U)

/* 位操作宏 */
#define SET_BIT(REG, BIT)     ((REG) |= (BIT))
#define CLEAR_BIT(REG, BIT)   ((REG) &= ~(BIT))
#define READ_BIT(REG, BIT)    ((REG) & (BIT))
#define UNUSED(X) (void)X

#ifdef __cplusplus
}
#endif

#endif /* __STM32F4xx_HAL_CONF_H */
```

4. **保存文件**: 按Ctrl+S保存

### 修改2：创建简化HAL实现文件

**需要创建新文件**: `STM32_Snake_Game\Source\stm32f4xx_hal_simple.c`

**操作步骤**:
1. 在Source文件夹中创建新文件 `stm32f4xx_hal_simple.c`
2. 用记事本打开该文件
3. **复制粘贴**以下内容:

```c
/**
 * @file stm32f4xx_hal_simple.c
 * @brief 简化的HAL库实现 - Keil版本
 */

#include "stm32f4xx_hal_conf.h"

/* 系统滴答计数器 */
volatile uint32_t uwTick = 0;

/**
 * @brief GPIO初始化函数
 */
void HAL_GPIO_Init(GPIO_TypeDef *GPIOx, GPIO_InitTypeDef *GPIO_Init)
{
    uint32_t position;
    uint32_t ioposition;
    uint32_t iocurrent;
    uint32_t temp;

    /* 配置GPIO引脚 */
    for (position = 0U; position < 16U; position++)
    {
        ioposition = 0x01U << position;
        iocurrent = (uint32_t)(GPIO_Init->Pin) & ioposition;

        if (iocurrent == ioposition)
        {
            /* 配置GPIO模式 */
            temp = GPIOx->MODER;
            temp &= ~(0x3U << (position * 2U));
            temp |= ((GPIO_Init->Mode & 0x3U) << (position * 2U));
            GPIOx->MODER = temp;

            /* 配置GPIO速度 */
            temp = GPIOx->OSPEEDR;
            temp &= ~(0x3U << (position * 2U));
            temp |= (GPIO_Init->Speed << (position * 2U));
            GPIOx->OSPEEDR = temp;

            /* 配置GPIO上拉下拉 */
            temp = GPIOx->PUPDR;
            temp &= ~(0x3U << (position * 2U));
            temp |= (GPIO_Init->Pull << (position * 2U));
            GPIOx->PUPDR = temp;
        }
    }
}

/**
 * @brief GPIO写引脚函数
 */
void HAL_GPIO_WritePin(GPIO_TypeDef *GPIOx, uint16_t GPIO_Pin, GPIO_PinState PinState)
{
    if (PinState != GPIO_PIN_RESET)
    {
        GPIOx->BSRR = GPIO_Pin;
    }
    else
    {
        GPIOx->BSRR = (uint32_t)GPIO_Pin << 16U;
    }
}

/**
 * @brief GPIO读引脚函数
 */
GPIO_PinState HAL_GPIO_ReadPin(GPIO_TypeDef *GPIOx, uint16_t GPIO_Pin)
{
    GPIO_PinState bitstatus;

    if ((GPIOx->IDR & GPIO_Pin) != (uint32_t)GPIO_PIN_RESET)
    {
        bitstatus = GPIO_PIN_SET;
    }
    else
    {
        bitstatus = GPIO_PIN_RESET;
    }
    return bitstatus;
}

/**
 * @brief 获取系统滴答计数
 */
uint32_t HAL_GetTick(void)
{
    return uwTick;
}

/**
 * @brief 延时函数
 */
void HAL_Delay(uint32_t Delay)
{
    uint32_t tickstart = HAL_GetTick();
    uint32_t wait = Delay;

    /* 确保延时至少为1ms */
    if (wait < 0xFFFFFFFFU)
    {
        wait += (uint32_t)(1U);
    }

    while ((HAL_GetTick() - tickstart) < wait)
    {
        /* 等待 */
    }
}

/**
 * @brief 系统滴答中断回调函数
 */
void HAL_IncTick(void)
{
    uwTick += 1U;
}
```

4. **保存文件**: 按Ctrl+S保存

### 修改3：在Keil项目中添加新文件

**重要**: 创建了新的HAL实现文件后，需要将其添加到Keil项目中:

1. **在Keil中右键System组**: 在项目窗口中右键点击 "System" 组
2. **添加文件**: 选择 "Add Existing Files to Group 'System'..."
3. **选择新文件**: 浏览到Source目录，选择 `stm32f4xx_hal_simple.c`
4. **添加**: 点击 "Add" 和 "Close"

## ✅ 修改完成验证

完成上述修改后:

1. **Source目录应包含7个文件**:
   - main.c
   - lcd_driver.c
   - snake_game.c
   - stm32f4xx_it.c
   - system_stm32f4xx.c
   - startup_stm32f411xe.s
   - stm32f4xx_hal_simple.c ← 新增

2. **Keil项目中System组应包含3个文件**:
   - stm32f4xx_it.c
   - system_stm32f4xx.c
   - stm32f4xx_hal_simple.c ← 新增

3. **重新编译项目**: 按F7重新编译，应该能成功编译

## 🚨 注意事项

1. **必须完成所有修改**: 这些修改是必需的，缺少任何一个都可能导致编译失败
2. **文件路径要正确**: 确保新创建的文件在正确的目录中
3. **添加到项目**: 新创建的文件必须添加到Keil项目中才能被编译
4. **保存所有文件**: 修改后记得保存所有文件

完成这些修改后，您的项目就能在Keil环境中正确编译了！
