# ARM GCC工具链前置知识学习指南

## 🎯 学习目标
为成功使用ARM GCC工具链开发STM32项目，需要掌握的基础知识和技能。

## 📋 知识清单概览

### 必备知识（⭐⭐⭐⭐⭐）
- [ ] 命令行基础操作
- [ ] 文件系统和路径概念
- [ ] 环境变量配置
- [ ] 基本的C语言编译过程

### 重要知识（⭐⭐⭐⭐）
- [ ] Makefile基础语法
- [ ] GCC编译器选项
- [ ] 链接过程理解
- [ ] 调试器基础使用

### 有用知识（⭐⭐⭐）
- [ ] 版本控制（Git）
- [ ] 文本编辑器高级功能
- [ ] 脚本编写基础
- [ ] 硬件调试概念

## 💻 1. 命令行基础操作

### 1.1 Windows命令行基础

#### 基本导航命令
```cmd
# 查看当前目录
cd

# 切换目录
cd C:\Users\<USER>\Desktop

# 返回上级目录
cd ..

# 列出文件和文件夹
dir

# 创建文件夹
mkdir new_folder

# 删除文件夹
rmdir /s folder_name

# 复制文件
copy source.txt destination.txt

# 移动文件
move source.txt new_location\
```

#### 环境变量操作
```cmd
# 查看环境变量
echo %PATH%

# 临时设置环境变量
set MY_VAR=value

# 查看所有环境变量
set
```

### 1.2 PowerShell基础（推荐）

#### 基本命令
```powershell
# 查看当前位置
Get-Location
# 或简写
pwd

# 切换目录
Set-Location C:\Users\<USER>\Desktop
# 或简写
cd C:\Users\<USER>\Desktop

# 列出文件
Get-ChildItem
# 或简写
ls

# 创建文件夹
New-Item -ItemType Directory -Name "new_folder"
# 或简写
mkdir new_folder

# 查看文件内容
Get-Content filename.txt
# 或简写
cat filename.txt
```

#### 管道和过滤
```powershell
# 查找包含特定文本的文件
Get-ChildItem | Where-Object {$_.Name -like "*.c"}

# 查看进程
Get-Process | Where-Object {$_.Name -like "*arm*"}
```

### 1.3 实践练习

#### 练习1：文件操作
```cmd
# 创建项目目录结构
mkdir STM32_Project
cd STM32_Project
mkdir src
mkdir include
mkdir build
mkdir docs

# 创建测试文件
echo "Hello STM32" > src\main.c
echo "// Header file" > include\main.h

# 查看目录结构
tree /f
```

#### 练习2：环境变量
```cmd
# 设置ARM GCC路径（示例）
set ARM_GCC_PATH=C:\Program Files (x86)\GNU Arm Embedded Toolchain\10 2021.10\bin

# 测试编译器
%ARM_GCC_PATH%\arm-none-eabi-gcc --version
```

## 📁 2. 文件系统和路径概念

### 2.1 绝对路径 vs 相对路径

#### 绝对路径
```
C:\Users\<USER>\Desktop\STM32_Project\src\main.c
```

#### 相对路径
```
# 当前在 STM32_Project 目录
src\main.c              # 相对于当前目录
..\Documents\file.txt    # 上级目录的Documents文件夹
..\..\Desktop\file.txt   # 上两级目录的Desktop文件夹
```

### 2.2 路径分隔符
- **Windows**：使用反斜杠 `\`
- **Linux/macOS**：使用正斜杠 `/`
- **跨平台**：在Makefile中通常使用正斜杠

### 2.3 特殊目录符号
```
.     # 当前目录
..    # 上级目录
~     # 用户主目录（PowerShell/Git Bash）
%CD%  # 当前目录（CMD）
```

## 🔧 3. 环境变量配置

### 3.1 Windows环境变量设置

#### 图形界面设置
1. 右键"此电脑" → "属性"
2. "高级系统设置" → "环境变量"
3. 在"系统变量"中找到"Path"
4. 点击"编辑" → "新建"
5. 添加ARM GCC的bin目录路径

#### 命令行设置
```cmd
# 临时设置（当前会话有效）
set PATH=%PATH%;C:\Program Files (x86)\GNU Arm Embedded Toolchain\10 2021.10\bin

# 永久设置（需要管理员权限）
setx PATH "%PATH%;C:\Program Files (x86)\GNU Arm Embedded Toolchain\10 2021.10\bin" /M
```

### 3.2 验证环境变量
```cmd
# 检查PATH
echo %PATH%

# 测试ARM GCC
arm-none-eabi-gcc --version
arm-none-eabi-objcopy --version
make --version
```

## 🔨 4. C语言编译过程理解

### 4.1 编译过程四个阶段

#### 预处理（Preprocessing）
```bash
# 展开宏定义，处理#include
arm-none-eabi-gcc -E main.c -o main.i
```

#### 编译（Compilation）
```bash
# 将C代码转换为汇编代码
arm-none-eabi-gcc -S main.c -o main.s
```

#### 汇编（Assembly）
```bash
# 将汇编代码转换为目标文件
arm-none-eabi-gcc -c main.c -o main.o
```

#### 链接（Linking）
```bash
# 将目标文件链接成可执行文件
arm-none-eabi-gcc main.o -o main.elf
```

### 4.2 常用编译选项
```bash
# 基本编译
arm-none-eabi-gcc -c main.c -o main.o

# 指定CPU架构
arm-none-eabi-gcc -mcpu=cortex-m4 -c main.c -o main.o

# 添加包含路径
arm-none-eabi-gcc -I./include -c main.c -o main.o

# 定义宏
arm-none-eabi-gcc -DSTM32F411xE -c main.c -o main.o

# 优化选项
arm-none-eabi-gcc -O2 -c main.c -o main.o

# 调试信息
arm-none-eabi-gcc -g -c main.c -o main.o
```

## 📝 5. 文本编辑器基础

### 5.1 推荐编辑器

#### Visual Studio Code（强烈推荐）
**优点**：
- 免费开源
- 丰富的插件生态
- 优秀的C/C++支持
- 集成终端

**必装插件**：
- C/C++
- ARM Assembly
- Makefile Tools
- GitLens

#### Notepad++（轻量级选择）
**优点**：
- 轻量快速
- 语法高亮
- 插件支持

### 5.2 VS Code配置示例

#### settings.json配置
```json
{
    "C_Cpp.default.compilerPath": "C:/Program Files (x86)/GNU Arm Embedded Toolchain/10 2021.10/bin/arm-none-eabi-gcc.exe",
    "C_Cpp.default.intelliSenseMode": "gcc-arm",
    "C_Cpp.default.includePath": [
        "${workspaceFolder}/**",
        "C:/Program Files (x86)/GNU Arm Embedded Toolchain/10 2021.10/arm-none-eabi/include/**"
    ],
    "C_Cpp.default.defines": [
        "STM32F411xE",
        "USE_HAL_DRIVER"
    ]
}
```

## 🧪 6. 实践练习项目

### 6.1 Hello World项目

#### 创建项目结构
```cmd
mkdir hello_stm32
cd hello_stm32
mkdir src include build
```

#### 创建简单的main.c
```c
// src/main.c
#include <stdint.h>

int main(void)
{
    volatile uint32_t counter = 0;
    
    while(1)
    {
        counter++;
    }
    
    return 0;
}
```

#### 手动编译测试
```bash
# 编译
arm-none-eabi-gcc -mcpu=cortex-m4 -mthumb -c src/main.c -o build/main.o

# 检查生成的目标文件
arm-none-eabi-objdump -h build/main.o
```

### 6.2 学习检查清单

#### 命令行操作检查
- [ ] 能够在不同目录间导航
- [ ] 能够创建、删除、复制文件和文件夹
- [ ] 理解绝对路径和相对路径
- [ ] 能够设置和查看环境变量

#### 编译过程检查
- [ ] 理解预处理、编译、汇编、链接四个阶段
- [ ] 能够使用基本的GCC编译选项
- [ ] 理解目标文件和可执行文件的区别
- [ ] 能够查看编译生成的文件信息

#### 工具使用检查
- [ ] 能够配置和使用文本编辑器
- [ ] 能够在命令行中运行编译器
- [ ] 能够查看和理解编译错误信息
- [ ] 能够使用基本的文件查看命令

## 📚 推荐学习资源

### 在线教程
1. **命令行基础**：
   - Microsoft Learn: PowerShell基础
   - Command Line Crash Course

2. **C语言编译**：
   - GCC官方文档
   - "An Introduction to GCC" 在线书籍

3. **文本编辑器**：
   - VS Code官方文档
   - "VS Code for C++" 教程

### 实践项目
1. 创建简单的C程序并手动编译
2. 练习不同的编译选项
3. 学习查看编译生成的文件
4. 练习文件和目录操作

掌握这些前置知识后，您就可以顺利进入ARM GCC工具链的学习了！
