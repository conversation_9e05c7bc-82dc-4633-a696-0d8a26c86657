/**
 * @file main.c
 * @brief STM32F411CEU6贪吃蛇游戏主程序
 * <AUTHOR> Assistant
 * @date 2024
 * 
 * 硬件连接:
 * LCD显示屏 (CL18CG958-18B):
 * PB2 → LCD_RESET (复位引脚)
 * PB3 → LCD_SCK   (SPI时钟引脚)
 * PB4 → LCD_DC    (数据/命令选择引脚)
 * PB5 → LCD_SDA   (SPI数据引脚)
 * PA15→ LCD_CS    (片选引脚)
 * 
 * 按键连接:
 * PA0 → KEY_UP    (上键)
 * PA1 → KEY_DOWN  (下键)
 * PA2 → KEY_LEFT  (左键)
 * PA3 → KEY_RIGHT (右键)
 */

#include "main.h"
#include "stm32f4xx_hal.h"
#include "snake_game.h"
#include <stdlib.h>
#include <time.h>

/* 私有函数声明 */
void SystemClock_Config(void);
void Error_Handler(void);

/**
 * @brief 主函数
 * @return 程序退出码
 */
int main(void)
{
    /* 重置所有外设，初始化Flash接口和SysTick */
    HAL_Init();
    
    /* 配置系统时钟 */
    SystemClock_Config();
    
    /* 初始化随机数种子 */
    srand(HAL_GetTick());
    
    /* 初始化游戏 */
    Game_Init();
    
    /* 主循环 */
    while(1)
    {
        /* 更新游戏状态 */
        Game_Update();
        
        /* 简单的延时，避免CPU占用过高 */
        HAL_Delay(10);
    }
}

/**
 * @brief 系统时钟配置
 * 配置系统时钟为84MHz
 */
void SystemClock_Config(void)
{
    RCC_OscInitTypeDef RCC_OscInitStruct = {0};
    RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
    
    /** 配置主内部调节器输出电压 */
    __HAL_RCC_PWR_CLK_ENABLE();
    __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE2);
    
    /** 初始化RCC振荡器 */
    RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
    RCC_OscInitStruct.HSIState = RCC_HSI_ON;
    RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
    RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
    RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
    RCC_OscInitStruct.PLL.PLLM = 16;
    RCC_OscInitStruct.PLL.PLLN = 336;
    RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV4;
    RCC_OscInitStruct.PLL.PLLQ = 7;
    
    if(HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
    {
        Error_Handler();
    }
    
    /** 初始化CPU、AHB和APB总线时钟 */
    RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK | RCC_CLOCKTYPE_SYSCLK |
                                  RCC_CLOCKTYPE_PCLK1 | RCC_CLOCKTYPE_PCLK2;
    RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
    RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
    RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;
    RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;
    
    if(HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
    {
        Error_Handler();
    }
}

/**
 * @brief 错误处理函数
 * 当发生错误时，此函数被调用
 */
void Error_Handler(void)
{
    /* 用户可以在这里添加自己的错误处理代码 */
    __disable_irq();
    while(1)
    {
        /* 无限循环 */
    }
}

/**
 * @brief 断言失败处理函数
 * @param file 文件名
 * @param line 行号
 */
#ifdef USE_FULL_ASSERT
void assert_failed(uint8_t *file, uint32_t line)
{
    /* 用户可以在这里添加自己的断言失败处理代码 */
    /* 例如: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
}
#endif /* USE_FULL_ASSERT */
