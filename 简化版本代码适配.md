# STM32项目简化版本代码适配指南

## 🎯 问题说明
原始代码使用了STM32 HAL库，但HAL库文件较大且配置复杂。为了让您能够快速编译成功，我提供一个简化版本的代码适配方案。

## 📝 第一步：修改stm32f4xx_hal_conf.h

将 `inc/stm32f4xx_hal_conf.h` 文件内容替换为以下简化版本：

```c
/**
 * @file stm32f4xx_hal_conf.h
 * @brief HAL配置文件 - 简化版本
 */

#ifndef __STM32F4xx_HAL_CONF_H
#define __STM32F4xx_HAL_CONF_H

#ifdef __cplusplus
extern "C" {
#endif

/* 基本类型定义 */
#include <stdint.h>
#include <stddef.h>

/* STM32F411xE芯片定义 */
#if !defined(STM32F411xE)
#define STM32F411xE
#endif

/* 基本寄存器地址定义 */
#define PERIPH_BASE           0x40000000UL
#define AHB1PERIPH_BASE       (PERIPH_BASE + 0x00020000UL)
#define GPIOA_BASE            (AHB1PERIPH_BASE + 0x0000UL)
#define GPIOB_BASE            (AHB1PERIPH_BASE + 0x0400UL)
#define RCC_BASE              (AHB1PERIPH_BASE + 0x3800UL)

/* GPIO寄存器结构体 */
typedef struct
{
    volatile uint32_t MODER;    /* GPIO端口模式寄存器 */
    volatile uint32_t OTYPER;   /* GPIO端口输出类型寄存器 */
    volatile uint32_t OSPEEDR;  /* GPIO端口输出速度寄存器 */
    volatile uint32_t PUPDR;    /* GPIO端口上拉/下拉寄存器 */
    volatile uint32_t IDR;      /* GPIO端口输入数据寄存器 */
    volatile uint32_t ODR;      /* GPIO端口输出数据寄存器 */
    volatile uint32_t BSRR;     /* GPIO端口位设置/复位寄存器 */
    volatile uint32_t LCKR;     /* GPIO端口配置锁定寄存器 */
    volatile uint32_t AFR[2];   /* GPIO备用功能寄存器 */
} GPIO_TypeDef;

/* RCC寄存器结构体 */
typedef struct
{
    volatile uint32_t CR;            /* RCC时钟控制寄存器 */
    volatile uint32_t PLLCFGR;       /* RCC PLL配置寄存器 */
    volatile uint32_t CFGR;          /* RCC时钟配置寄存器 */
    volatile uint32_t CIR;           /* RCC时钟中断寄存器 */
    volatile uint32_t AHB1RSTR;      /* RCC AHB1外设复位寄存器 */
    volatile uint32_t AHB2RSTR;      /* RCC AHB2外设复位寄存器 */
    volatile uint32_t AHB3RSTR;      /* RCC AHB3外设复位寄存器 */
    uint32_t      RESERVED0;         /* 保留 */
    volatile uint32_t APB1RSTR;      /* RCC APB1外设复位寄存器 */
    volatile uint32_t APB2RSTR;      /* RCC APB2外设复位寄存器 */
    uint32_t      RESERVED1[2];      /* 保留 */
    volatile uint32_t AHB1ENR;       /* RCC AHB1外设时钟使能寄存器 */
    volatile uint32_t AHB2ENR;       /* RCC AHB2外设时钟使能寄存器 */
    volatile uint32_t AHB3ENR;       /* RCC AHB3外设时钟使能寄存器 */
    uint32_t      RESERVED2;         /* 保留 */
    volatile uint32_t APB1ENR;       /* RCC APB1外设时钟使能寄存器 */
    volatile uint32_t APB2ENR;       /* RCC APB2外设时钟使能寄存器 */
} RCC_TypeDef;

/* 外设指针定义 */
#define GPIOA               ((GPIO_TypeDef *) GPIOA_BASE)
#define GPIOB               ((GPIO_TypeDef *) GPIOB_BASE)
#define RCC                 ((RCC_TypeDef *) RCC_BASE)

/* GPIO引脚定义 */
#define GPIO_PIN_0          0x0001U
#define GPIO_PIN_1          0x0002U
#define GPIO_PIN_2          0x0004U
#define GPIO_PIN_3          0x0008U
#define GPIO_PIN_4          0x0010U
#define GPIO_PIN_5          0x0020U
#define GPIO_PIN_13         0x2000U
#define GPIO_PIN_15         0x8000U

/* GPIO模式定义 */
#define GPIO_MODE_INPUT     0x00000000U
#define GPIO_MODE_OUTPUT_PP 0x00000001U
#define GPIO_MODE_OUTPUT_OD 0x00000011U
#define GPIO_MODE_AF_PP     0x00000002U

/* GPIO速度定义 */
#define GPIO_SPEED_FREQ_LOW    0x00000000U
#define GPIO_SPEED_FREQ_MEDIUM 0x00000001U
#define GPIO_SPEED_FREQ_HIGH   0x00000002U
#define GPIO_SPEED_FREQ_VERY_HIGH 0x00000003U

/* GPIO上拉下拉定义 */
#define GPIO_NOPULL         0x00000000U
#define GPIO_PULLUP         0x00000001U
#define GPIO_PULLDOWN       0x00000002U

/* GPIO状态定义 */
typedef enum
{
    GPIO_PIN_RESET = 0,
    GPIO_PIN_SET
} GPIO_PinState;

/* GPIO初始化结构体 */
typedef struct
{
    uint32_t Pin;       /* GPIO引脚 */
    uint32_t Mode;      /* GPIO模式 */
    uint32_t Pull;      /* GPIO上拉下拉 */
    uint32_t Speed;     /* GPIO速度 */
} GPIO_InitTypeDef;

/* RCC时钟使能位定义 */
#define RCC_AHB1ENR_GPIOAEN 0x00000001U
#define RCC_AHB1ENR_GPIOBEN 0x00000002U

/* 系统滴答定时器 */
extern volatile uint32_t uwTick;

/* 函数声明 */
void HAL_GPIO_Init(GPIO_TypeDef *GPIOx, GPIO_InitTypeDef *GPIO_Init);
void HAL_GPIO_WritePin(GPIO_TypeDef *GPIOx, uint16_t GPIO_Pin, GPIO_PinState PinState);
GPIO_PinState HAL_GPIO_ReadPin(GPIO_TypeDef *GPIOx, uint16_t GPIO_Pin);
uint32_t HAL_GetTick(void);
void HAL_Delay(uint32_t Delay);
void HAL_IncTick(void);

/* 时钟使能宏 */
#define __HAL_RCC_GPIOA_CLK_ENABLE()   do { \
                                        uint32_t tmpreg = 0x00U; \
                                        SET_BIT(RCC->AHB1ENR, RCC_AHB1ENR_GPIOAEN);\
                                        tmpreg = READ_BIT(RCC->AHB1ENR, RCC_AHB1ENR_GPIOAEN);\
                                        UNUSED(tmpreg); \
                                      } while(0U)

#define __HAL_RCC_GPIOB_CLK_ENABLE()   do { \
                                        uint32_t tmpreg = 0x00U; \
                                        SET_BIT(RCC->AHB1ENR, RCC_AHB1ENR_GPIOBEN);\
                                        tmpreg = READ_BIT(RCC->AHB1ENR, RCC_AHB1ENR_GPIOBEN);\
                                        UNUSED(tmpreg); \
                                      } while(0U)

/* 位操作宏 */
#define SET_BIT(REG, BIT)     ((REG) |= (BIT))
#define CLEAR_BIT(REG, BIT)   ((REG) &= ~(BIT))
#define READ_BIT(REG, BIT)    ((REG) & (BIT))
#define UNUSED(X) (void)X

#ifdef __cplusplus
}
#endif

#endif /* __STM32F4xx_HAL_CONF_H */
```

## 📝 第二步：创建简化的HAL实现

创建新文件 `src/stm32f4xx_hal_simple.c`：

```c
/**
 * @file stm32f4xx_hal_simple.c
 * @brief 简化的HAL库实现
 */

#include "stm32f4xx_hal_conf.h"

/* 系统滴答计数器 */
volatile uint32_t uwTick = 0;

/**
 * @brief GPIO初始化函数
 */
void HAL_GPIO_Init(GPIO_TypeDef *GPIOx, GPIO_InitTypeDef *GPIO_Init)
{
    uint32_t position;
    uint32_t ioposition;
    uint32_t iocurrent;
    uint32_t temp;

    /* 配置GPIO引脚 */
    for (position = 0U; position < 16U; position++)
    {
        ioposition = 0x01U << position;
        iocurrent = (uint32_t)(GPIO_Init->Pin) & ioposition;

        if (iocurrent == ioposition)
        {
            /* 配置GPIO模式 */
            temp = GPIOx->MODER;
            temp &= ~(0x3U << (position * 2U));
            temp |= ((GPIO_Init->Mode & 0x3U) << (position * 2U));
            GPIOx->MODER = temp;

            /* 配置GPIO速度 */
            temp = GPIOx->OSPEEDR;
            temp &= ~(0x3U << (position * 2U));
            temp |= (GPIO_Init->Speed << (position * 2U));
            GPIOx->OSPEEDR = temp;

            /* 配置GPIO上拉下拉 */
            temp = GPIOx->PUPDR;
            temp &= ~(0x3U << (position * 2U));
            temp |= (GPIO_Init->Pull << (position * 2U));
            GPIOx->PUPDR = temp;
        }
    }
}

/**
 * @brief GPIO写引脚函数
 */
void HAL_GPIO_WritePin(GPIO_TypeDef *GPIOx, uint16_t GPIO_Pin, GPIO_PinState PinState)
{
    if (PinState != GPIO_PIN_RESET)
    {
        GPIOx->BSRR = GPIO_Pin;
    }
    else
    {
        GPIOx->BSRR = (uint32_t)GPIO_Pin << 16U;
    }
}

/**
 * @brief GPIO读引脚函数
 */
GPIO_PinState HAL_GPIO_ReadPin(GPIO_TypeDef *GPIOx, uint16_t GPIO_Pin)
{
    GPIO_PinState bitstatus;

    if ((GPIOx->IDR & GPIO_Pin) != (uint32_t)GPIO_PIN_RESET)
    {
        bitstatus = GPIO_PIN_SET;
    }
    else
    {
        bitstatus = GPIO_PIN_RESET;
    }
    return bitstatus;
}

/**
 * @brief 获取系统滴答计数
 */
uint32_t HAL_GetTick(void)
{
    return uwTick;
}

/**
 * @brief 延时函数
 */
void HAL_Delay(uint32_t Delay)
{
    uint32_t tickstart = HAL_GetTick();
    uint32_t wait = Delay;

    /* 确保延时至少为1ms */
    if (wait < 0xFFFFFFFFU)
    {
        wait += (uint32_t)(1U);
    }

    while ((HAL_GetTick() - tickstart) < wait)
    {
        /* 等待 */
    }
}

/**
 * @brief 系统滴答中断回调函数
 */
void HAL_IncTick(void)
{
    uwTick += 1U;
}
```

## 📝 第三步：修改Makefile

将Makefile中的源文件列表修改为：

```makefile
# C源文件
C_SOURCES =  \
src/main.c \
src/lcd_driver.c \
src/snake_game.c \
src/stm32f4xx_it.c \
src/system_stm32f4xx.c \
src/stm32f4xx_hal_simple.c
```

## 📝 第四步：修改system_stm32f4xx.c

将 `src/system_stm32f4xx.c` 文件简化为：

```c
/**
 * @file system_stm32f4xx.c
 * @brief STM32F4xx系统初始化文件 - 简化版本
 */

#include "stm32f4xx_hal_conf.h"

/* 系统时钟频率 */
uint32_t SystemCoreClock = 16000000;

/**
 * @brief 系统初始化函数
 */
void SystemInit(void)
{
    /* 基本的系统初始化 */
    /* 在这里可以添加时钟配置等 */
}

/**
 * @brief 更新系统时钟频率变量
 */
void SystemCoreClockUpdate(void)
{
    SystemCoreClock = 16000000;
}
```

## 📝 第五步：修改中断文件

确保 `src/stm32f4xx_it.c` 文件包含以下内容：

```c
/**
 * @brief 系统滴答定时器中断处理函数
 */
void SysTick_Handler(void)
{
    HAL_IncTick();
}
```

## 🔨 第六步：重新编译

1. 清理之前的编译文件：
   ```cmd
   make clean
   ```

2. 重新编译项目：
   ```cmd
   make all
   ```

## ✅ 预期结果

使用简化版本后，编译应该能够成功完成，生成的程序大小会更小，更适合学习和测试。

**注意**：这个简化版本去除了复杂的HAL库依赖，但保留了核心功能，足以运行贪吃蛇游戏。

## 🚨 如果仍有问题

如果编译仍然出现错误，请：

1. 检查所有文件是否在正确的目录中
2. 确认文件名拼写正确
3. 验证ARM GCC工具链安装
4. 查看具体的错误信息并告诉我

这个简化版本应该能够让您成功编译出可用的HEX文件！
