@echo off
chcp 65001 >nul
echo ========================================
echo STM32F411CEU6贪吃蛇游戏源代码文件获取
echo ========================================
echo.

:: 设置项目目录
set PROJECT_DIR=%USERPROFILE%\Desktop\STM32_Snake_Game

echo 项目将创建在: %PROJECT_DIR%
echo.

:: 创建项目目录结构
echo 1. 创建项目目录结构...
if exist "%PROJECT_DIR%" (
    echo 目录已存在，是否删除重新创建？ (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        rmdir /s /q "%PROJECT_DIR%"
        echo 已删除旧目录
    )
)

mkdir "%PROJECT_DIR%" 2>nul
mkdir "%PROJECT_DIR%\Project" 2>nul
mkdir "%PROJECT_DIR%\Source" 2>nul
mkdir "%PROJECT_DIR%\Include" 2>nul
mkdir "%PROJECT_DIR%\Output" 2>nul
mkdir "%PROJECT_DIR%\Docs" 2>nul

echo [成功] 目录结构创建完成
echo.

:: 检查当前目录中的源文件
echo 2. 检查当前目录中的源文件...
set all_files_exist=1

:: 检查源代码文件
if not exist "main.c" (
    echo [缺失] main.c
    set all_files_exist=0
)
if not exist "lcd_driver.c" (
    echo [缺失] lcd_driver.c
    set all_files_exist=0
)
if not exist "snake_game.c" (
    echo [缺失] snake_game.c
    set all_files_exist=0
)
if not exist "stm32f4xx_it.c" (
    echo [缺失] stm32f4xx_it.c
    set all_files_exist=0
)
if not exist "system_stm32f4xx.c" (
    echo [缺失] system_stm32f4xx.c
    set all_files_exist=0
)
if not exist "startup_stm32f411xe.s" (
    echo [缺失] startup_stm32f411xe.s
    set all_files_exist=0
)

:: 检查头文件
if not exist "main.h" (
    echo [缺失] main.h
    set all_files_exist=0
)
if not exist "lcd_driver.h" (
    echo [缺失] lcd_driver.h
    set all_files_exist=0
)
if not exist "snake_game.h" (
    echo [缺失] snake_game.h
    set all_files_exist=0
)
if not exist "stm32f4xx_it.h" (
    echo [缺失] stm32f4xx_it.h
    set all_files_exist=0
)
if not exist "stm32f4xx_hal_conf.h" (
    echo [缺失] stm32f4xx_hal_conf.h
    set all_files_exist=0
)

if %all_files_exist%==1 (
    echo [成功] 所有源文件都存在
) else (
    echo [错误] 部分源文件缺失
    echo.
    echo 请确保以下文件都在当前目录中:
    echo 源代码文件: main.c, lcd_driver.c, snake_game.c, stm32f4xx_it.c, system_stm32f4xx.c, startup_stm32f411xe.s
    echo 头文件: main.h, lcd_driver.h, snake_game.h, stm32f4xx_it.h, stm32f4xx_hal_conf.h
    echo.
    pause
    exit /b 1
)

:: 复制源代码文件
echo.
echo 3. 复制源代码文件到项目目录...

echo 复制源代码文件到 Source 目录:
copy "main.c" "%PROJECT_DIR%\Source\" >nul && echo   ✅ main.c
copy "lcd_driver.c" "%PROJECT_DIR%\Source\" >nul && echo   ✅ lcd_driver.c
copy "snake_game.c" "%PROJECT_DIR%\Source\" >nul && echo   ✅ snake_game.c
copy "stm32f4xx_it.c" "%PROJECT_DIR%\Source\" >nul && echo   ✅ stm32f4xx_it.c
copy "system_stm32f4xx.c" "%PROJECT_DIR%\Source\" >nul && echo   ✅ system_stm32f4xx.c
copy "startup_stm32f411xe.s" "%PROJECT_DIR%\Source\" >nul && echo   ✅ startup_stm32f411xe.s

echo.
echo 复制头文件到 Include 目录:
copy "main.h" "%PROJECT_DIR%\Include\" >nul && echo   ✅ main.h
copy "lcd_driver.h" "%PROJECT_DIR%\Include\" >nul && echo   ✅ lcd_driver.h
copy "snake_game.h" "%PROJECT_DIR%\Include\" >nul && echo   ✅ snake_game.h
copy "stm32f4xx_it.h" "%PROJECT_DIR%\Include\" >nul && echo   ✅ stm32f4xx_it.h
copy "stm32f4xx_hal_conf.h" "%PROJECT_DIR%\Include\" >nul && echo   ✅ stm32f4xx_hal_conf.h

:: 创建项目说明文件
echo.
echo 4. 创建项目说明文件...
(
echo # STM32F411CEU6贪吃蛇游戏项目
echo.
echo ## 项目文件结构
echo ```
echo STM32_Snake_Game/
echo ├── Project/        ^(Keil项目文件^)
echo ├── Source/         ^(源代码文件^)
echo │   ├── main.c
echo │   ├── lcd_driver.c
echo │   ├── snake_game.c
echo │   ├── stm32f4xx_it.c
echo │   ├── system_stm32f4xx.c
echo │   └── startup_stm32f411xe.s
echo ├── Include/        ^(头文件^)
echo │   ├── main.h
echo │   ├── lcd_driver.h
echo │   ├── snake_game.h
echo │   ├── stm32f4xx_it.h
echo │   └── stm32f4xx_hal_conf.h
echo ├── Output/         ^(编译输出^)
echo └── Docs/           ^(文档^)
echo ```
echo.
echo ## 下一步操作
echo 1. 启动Keil uVision5
echo 2. 按照详细操作指南创建项目
echo 3. 添加源文件并编译
echo.
echo ## 硬件连接
echo ### LCD显示屏 ^(CL18CG958-18B^)
echo ```
echo STM32F411CEU6    →    LCD
echo PB2              →    RESET
echo PB3              →    SCK
echo PB4              →    DC
echo PB5              →    SDA
echo PA15             →    CS
echo 3.3V             →    VCC
echo GND              →    GND
echo ```
echo.
echo ### 按键连接
echo ```
echo STM32F411CEU6    →    按键
echo PA0              →    上键 ^(连接到GND^)
echo PA1              →    下键 ^(连接到GND^)
echo PA2              →    左键 ^(连接到GND^)
echo PA3              →    右键 ^(连接到GND^)
echo ```
echo.
echo 项目文件准备完成！🐍🎮
) > "%PROJECT_DIR%\README.md"

:: 复制操作指南文档
echo 5. 复制操作指南文档...
if exist "Keil详细操作步骤.md" (
    copy "Keil详细操作步骤.md" "%PROJECT_DIR%\Docs\" >nul
    echo   ✅ Keil详细操作步骤.md
)
if exist "Keil源代码适配修改.md" (
    copy "Keil源代码适配修改.md" "%PROJECT_DIR%\Docs\" >nul
    echo   ✅ Keil源代码适配修改.md
)
if exist "HEX文件验证指南.md" (
    copy "HEX文件验证指南.md" "%PROJECT_DIR%\Docs\" >nul
    echo   ✅ HEX文件验证指南.md
)

:: 显示完成信息
echo.
echo ========================================
echo 🎉 源代码文件获取完成！
echo ========================================
echo.
echo 📁 项目位置: %PROJECT_DIR%
echo.
echo 📋 文件统计:
echo   ✅ Source/     - 6个源代码文件
echo   ✅ Include/    - 5个头文件
echo   ✅ Project/    - Keil项目目录 ^(空^)
echo   ✅ Output/     - 编译输出目录 ^(空^)
echo   ✅ Docs/       - 操作指南文档
echo   ✅ README.md   - 项目说明
echo.
echo 🔧 下一步操作:
echo   1. 打开项目文件夹
echo   2. 阅读操作指南文档
echo   3. 启动Keil uVision5
echo   4. 按照指南创建项目
echo.

:: 询问是否打开项目文件夹
echo 是否现在打开项目文件夹？ (Y/N)
set /p open_folder=
if /i "%open_folder%"=="Y" (
    explorer "%PROJECT_DIR%"
)

echo.
echo 按任意键退出...
pause >nul
