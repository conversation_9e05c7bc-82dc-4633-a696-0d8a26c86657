# STM32F411CEU6贪吃蛇游戏 - Keil源代码适配修改

## 🎯 修改目的
确保源代码能够在Keil uVision5环境中正确编译，解决可能的兼容性问题。

## 📝 需要修改的文件

### 1. stm32f4xx_hal_conf.h 文件修改

**文件位置**：`C:\Users\<USER>\Desktop\STM32_Snake_Keil\Include\stm32f4xx_hal_conf.h`

**修改原因**：Keil环境需要简化的HAL配置以避免复杂的库依赖

**具体修改内容**：
将整个文件内容替换为以下简化版本：

```c
/**
 * @file stm32f4xx_hal_conf.h
 * @brief HAL配置文件 - Keil简化版本
 */

#ifndef __STM32F4xx_HAL_CONF_H
#define __STM32F4xx_HAL_CONF_H

#ifdef __cplusplus
extern "C" {
#endif

/* 基本类型定义 */
#include <stdint.h>
#include <stddef.h>

/* STM32F411xE芯片定义 */
#if !defined(STM32F411xE)
#define STM32F411xE
#endif

/* 基本寄存器地址定义 */
#define PERIPH_BASE           0x40000000UL
#define AHB1PERIPH_BASE       (PERIPH_BASE + 0x00020000UL)
#define GPIOA_BASE            (AHB1PERIPH_BASE + 0x0000UL)
#define GPIOB_BASE            (AHB1PERIPH_BASE + 0x0400UL)
#define RCC_BASE              (AHB1PERIPH_BASE + 0x3800UL)

/* GPIO寄存器结构体 */
typedef struct
{
    volatile uint32_t MODER;    /* GPIO端口模式寄存器 */
    volatile uint32_t OTYPER;   /* GPIO端口输出类型寄存器 */
    volatile uint32_t OSPEEDR;  /* GPIO端口输出速度寄存器 */
    volatile uint32_t PUPDR;    /* GPIO端口上拉/下拉寄存器 */
    volatile uint32_t IDR;      /* GPIO端口输入数据寄存器 */
    volatile uint32_t ODR;      /* GPIO端口输出数据寄存器 */
    volatile uint32_t BSRR;     /* GPIO端口位设置/复位寄存器 */
    volatile uint32_t LCKR;     /* GPIO端口配置锁定寄存器 */
    volatile uint32_t AFR[2];   /* GPIO备用功能寄存器 */
} GPIO_TypeDef;

/* RCC寄存器结构体 */
typedef struct
{
    volatile uint32_t CR;            /* RCC时钟控制寄存器 */
    volatile uint32_t PLLCFGR;       /* RCC PLL配置寄存器 */
    volatile uint32_t CFGR;          /* RCC时钟配置寄存器 */
    volatile uint32_t CIR;           /* RCC时钟中断寄存器 */
    volatile uint32_t AHB1RSTR;      /* RCC AHB1外设复位寄存器 */
    volatile uint32_t AHB2RSTR;      /* RCC AHB2外设复位寄存器 */
    volatile uint32_t AHB3RSTR;      /* RCC AHB3外设复位寄存器 */
    uint32_t      RESERVED0;         /* 保留 */
    volatile uint32_t APB1RSTR;      /* RCC APB1外设复位寄存器 */
    volatile uint32_t APB2RSTR;      /* RCC APB2外设复位寄存器 */
    uint32_t      RESERVED1[2];      /* 保留 */
    volatile uint32_t AHB1ENR;       /* RCC AHB1外设时钟使能寄存器 */
    volatile uint32_t AHB2ENR;       /* RCC AHB2外设时钟使能寄存器 */
    volatile uint32_t AHB3ENR;       /* RCC AHB3外设时钟使能寄存器 */
    uint32_t      RESERVED2;         /* 保留 */
    volatile uint32_t APB1ENR;       /* RCC APB1外设时钟使能寄存器 */
    volatile uint32_t APB2ENR;       /* RCC APB2外设时钟使能寄存器 */
} RCC_TypeDef;

/* 外设指针定义 */
#define GPIOA               ((GPIO_TypeDef *) GPIOA_BASE)
#define GPIOB               ((GPIO_TypeDef *) GPIOB_BASE)
#define RCC                 ((RCC_TypeDef *) RCC_BASE)

/* GPIO引脚定义 */
#define GPIO_PIN_0          0x0001U
#define GPIO_PIN_1          0x0002U
#define GPIO_PIN_2          0x0004U
#define GPIO_PIN_3          0x0008U
#define GPIO_PIN_4          0x0010U
#define GPIO_PIN_5          0x0020U
#define GPIO_PIN_13         0x2000U
#define GPIO_PIN_15         0x8000U

/* GPIO模式定义 */
#define GPIO_MODE_INPUT     0x00000000U
#define GPIO_MODE_OUTPUT_PP 0x00000001U
#define GPIO_MODE_OUTPUT_OD 0x00000011U
#define GPIO_MODE_AF_PP     0x00000002U

/* GPIO速度定义 */
#define GPIO_SPEED_FREQ_LOW    0x00000000U
#define GPIO_SPEED_FREQ_MEDIUM 0x00000001U
#define GPIO_SPEED_FREQ_HIGH   0x00000002U
#define GPIO_SPEED_FREQ_VERY_HIGH 0x00000003U

/* GPIO上拉下拉定义 */
#define GPIO_NOPULL         0x00000000U
#define GPIO_PULLUP         0x00000001U
#define GPIO_PULLDOWN       0x00000002U

/* GPIO状态定义 */
typedef enum
{
    GPIO_PIN_RESET = 0,
    GPIO_PIN_SET
} GPIO_PinState;

/* GPIO初始化结构体 */
typedef struct
{
    uint32_t Pin;       /* GPIO引脚 */
    uint32_t Mode;      /* GPIO模式 */
    uint32_t Pull;      /* GPIO上拉下拉 */
    uint32_t Speed;     /* GPIO速度 */
} GPIO_InitTypeDef;

/* RCC时钟使能位定义 */
#define RCC_AHB1ENR_GPIOAEN 0x00000001U
#define RCC_AHB1ENR_GPIOBEN 0x00000002U

/* 系统滴答定时器 */
extern volatile uint32_t uwTick;

/* 函数声明 */
void HAL_GPIO_Init(GPIO_TypeDef *GPIOx, GPIO_InitTypeDef *GPIO_Init);
void HAL_GPIO_WritePin(GPIO_TypeDef *GPIOx, uint16_t GPIO_Pin, GPIO_PinState PinState);
GPIO_PinState HAL_GPIO_ReadPin(GPIO_TypeDef *GPIOx, uint16_t GPIO_Pin);
uint32_t HAL_GetTick(void);
void HAL_Delay(uint32_t Delay);
void HAL_IncTick(void);

/* 时钟使能宏 */
#define __HAL_RCC_GPIOA_CLK_ENABLE()   do { \
                                        uint32_t tmpreg = 0x00U; \
                                        SET_BIT(RCC->AHB1ENR, RCC_AHB1ENR_GPIOAEN);\
                                        tmpreg = READ_BIT(RCC->AHB1ENR, RCC_AHB1ENR_GPIOAEN);\
                                        UNUSED(tmpreg); \
                                      } while(0U)

#define __HAL_RCC_GPIOB_CLK_ENABLE()   do { \
                                        uint32_t tmpreg = 0x00U; \
                                        SET_BIT(RCC->AHB1ENR, RCC_AHB1ENR_GPIOBEN);\
                                        tmpreg = READ_BIT(RCC->AHB1ENR, RCC_AHB1ENR_GPIOBEN);\
                                        UNUSED(tmpreg); \
                                      } while(0U)

/* 位操作宏 */
#define SET_BIT(REG, BIT)     ((REG) |= (BIT))
#define CLEAR_BIT(REG, BIT)   ((REG) &= ~(BIT))
#define READ_BIT(REG, BIT)    ((REG) & (BIT))
#define UNUSED(X) (void)X

#ifdef __cplusplus
}
#endif

#endif /* __STM32F4xx_HAL_CONF_H */
```

### 2. 创建简化的HAL实现文件

**需要创建新文件**：`C:\Users\<USER>\Desktop\STM32_Snake_Keil\Source\stm32f4xx_hal_simple.c`

**文件内容**：
```c
/**
 * @file stm32f4xx_hal_simple.c
 * @brief 简化的HAL库实现 - Keil版本
 */

#include "stm32f4xx_hal_conf.h"

/* 系统滴答计数器 */
volatile uint32_t uwTick = 0;

/**
 * @brief GPIO初始化函数
 */
void HAL_GPIO_Init(GPIO_TypeDef *GPIOx, GPIO_InitTypeDef *GPIO_Init)
{
    uint32_t position;
    uint32_t ioposition;
    uint32_t iocurrent;
    uint32_t temp;

    /* 配置GPIO引脚 */
    for (position = 0U; position < 16U; position++)
    {
        ioposition = 0x01U << position;
        iocurrent = (uint32_t)(GPIO_Init->Pin) & ioposition;

        if (iocurrent == ioposition)
        {
            /* 配置GPIO模式 */
            temp = GPIOx->MODER;
            temp &= ~(0x3U << (position * 2U));
            temp |= ((GPIO_Init->Mode & 0x3U) << (position * 2U));
            GPIOx->MODER = temp;

            /* 配置GPIO速度 */
            temp = GPIOx->OSPEEDR;
            temp &= ~(0x3U << (position * 2U));
            temp |= (GPIO_Init->Speed << (position * 2U));
            GPIOx->OSPEEDR = temp;

            /* 配置GPIO上拉下拉 */
            temp = GPIOx->PUPDR;
            temp &= ~(0x3U << (position * 2U));
            temp |= (GPIO_Init->Pull << (position * 2U));
            GPIOx->PUPDR = temp;
        }
    }
}

/**
 * @brief GPIO写引脚函数
 */
void HAL_GPIO_WritePin(GPIO_TypeDef *GPIOx, uint16_t GPIO_Pin, GPIO_PinState PinState)
{
    if (PinState != GPIO_PIN_RESET)
    {
        GPIOx->BSRR = GPIO_Pin;
    }
    else
    {
        GPIOx->BSRR = (uint32_t)GPIO_Pin << 16U;
    }
}

/**
 * @brief GPIO读引脚函数
 */
GPIO_PinState HAL_GPIO_ReadPin(GPIO_TypeDef *GPIOx, uint16_t GPIO_Pin)
{
    GPIO_PinState bitstatus;

    if ((GPIOx->IDR & GPIO_Pin) != (uint32_t)GPIO_PIN_RESET)
    {
        bitstatus = GPIO_PIN_SET;
    }
    else
    {
        bitstatus = GPIO_PIN_RESET;
    }
    return bitstatus;
}

/**
 * @brief 获取系统滴答计数
 */
uint32_t HAL_GetTick(void)
{
    return uwTick;
}

/**
 * @brief 延时函数
 */
void HAL_Delay(uint32_t Delay)
{
    uint32_t tickstart = HAL_GetTick();
    uint32_t wait = Delay;

    /* 确保延时至少为1ms */
    if (wait < 0xFFFFFFFFU)
    {
        wait += (uint32_t)(1U);
    }

    while ((HAL_GetTick() - tickstart) < wait)
    {
        /* 等待 */
    }
}

/**
 * @brief 系统滴答中断回调函数
 */
void HAL_IncTick(void)
{
    uwTick += 1U;
}
```

### 3. 修改system_stm32f4xx.c文件

**文件位置**：`C:\Users\<USER>\Desktop\STM32_Snake_Keil\Source\system_stm32f4xx.c`

**修改内容**：确保文件开头包含正确的头文件引用：

```c
/**
 * @file system_stm32f4xx.c
 * @brief STM32F4xx系统初始化文件
 */

#include "stm32f4xx_hal_conf.h"

/* 其余内容保持不变 */
```

### 4. 修改stm32f4xx_it.c文件

**文件位置**：`C:\Users\<USER>\Desktop\STM32_Snake_Keil\Source\stm32f4xx_it.c`

**确保包含以下内容**：

```c
/**
 * @brief 系统滴答定时器中断处理函数
 */
void SysTick_Handler(void)
{
    HAL_IncTick();
}
```

## 🔧 修改步骤

### 步骤1：修改HAL配置文件
1. 用记事本或其他文本编辑器打开：
   `C:\Users\<USER>\Desktop\STM32_Snake_Keil\Include\stm32f4xx_hal_conf.h`
2. 删除所有内容
3. 复制粘贴上面提供的简化版本内容
4. 保存文件

### 步骤2：创建简化HAL实现文件
1. 在 `C:\Users\<USER>\Desktop\STM32_Snake_Keil\Source\` 目录中创建新文件：
   `stm32f4xx_hal_simple.c`
2. 复制粘贴上面提供的HAL实现内容
3. 保存文件

### 步骤3：在Keil项目中添加新文件
1. 在Keil项目中，右键点击 **"System"** 组
2. 选择 **"Add Existing Files to Group 'System'..."**
3. 选择刚创建的 `stm32f4xx_hal_simple.c` 文件
4. 点击 **"Add"** 和 **"Close"**

## ✅ 修改完成验证

完成上述修改后，您的项目应该能够在Keil中正确编译。

**最终的Source目录应包含7个文件**：
- main.c
- lcd_driver.c
- snake_game.c
- stm32f4xx_it.c
- system_stm32f4xx.c
- startup_stm32f411xe.s
- stm32f4xx_hal_simple.c ← 新增文件

这些修改确保了代码能够在Keil环境中正确编译，同时保持了原有的功能。
