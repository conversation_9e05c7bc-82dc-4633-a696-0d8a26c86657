@echo off
chcp 65001 >nul
echo ========================================
echo STM32F411CEU6贪吃蛇游戏 - Keil项目创建脚本
echo ========================================
echo.

:: 设置项目目录名称
set PROJECT_DIR=STM32_Snake_Keil

:: 检查当前目录是否包含源文件
echo 1. 检查源文件...
set missing_files=0

if not exist "main.c" (
    echo [错误] 缺少文件: main.c
    set missing_files=1
)

if not exist "lcd_driver.c" (
    echo [错误] 缺少文件: lcd_driver.c
    set missing_files=1
)

if not exist "snake_game.c" (
    echo [错误] 缺少文件: snake_game.c
    set missing_files=1
)

if not exist "stm32f4xx_it.c" (
    echo [错误] 缺少文件: stm32f4xx_it.c
    set missing_files=1
)

if not exist "system_stm32f4xx.c" (
    echo [错误] 缺少文件: system_stm32f4xx.c
    set missing_files=1
)

if not exist "startup_stm32f411xe.s" (
    echo [错误] 缺少文件: startup_stm32f411xe.s
    set missing_files=1
)

if %missing_files% equ 1 (
    echo.
    echo 请确保所有源文件都在当前目录中
    pause
    exit /b 1
)
echo [成功] 所有源文件检查完毕

:: 创建项目目录结构
echo.
echo 2. 创建项目目录结构...

if exist "%PROJECT_DIR%" (
    echo [警告] 目录 %PROJECT_DIR% 已存在
    echo 是否要删除并重新创建？ (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        rmdir /s /q "%PROJECT_DIR%"
        echo [信息] 已删除旧目录
    ) else (
        echo [信息] 保留现有目录
        goto :copy_files
    )
)

mkdir "%PROJECT_DIR%"
mkdir "%PROJECT_DIR%\Project"
mkdir "%PROJECT_DIR%\Source"
mkdir "%PROJECT_DIR%\Include"
mkdir "%PROJECT_DIR%\Output"
echo [成功] 项目目录结构创建完成

:copy_files
:: 复制源代码文件
echo.
echo 3. 复制源代码文件到Source目录...
copy "main.c" "%PROJECT_DIR%\Source\" >nul
copy "lcd_driver.c" "%PROJECT_DIR%\Source\" >nul
copy "snake_game.c" "%PROJECT_DIR%\Source\" >nul
copy "stm32f4xx_it.c" "%PROJECT_DIR%\Source\" >nul
copy "system_stm32f4xx.c" "%PROJECT_DIR%\Source\" >nul
copy "startup_stm32f411xe.s" "%PROJECT_DIR%\Source\" >nul
echo [成功] 源代码文件复制完成

:: 复制头文件
echo.
echo 4. 复制头文件到Include目录...
copy "main.h" "%PROJECT_DIR%\Include\" >nul
copy "lcd_driver.h" "%PROJECT_DIR%\Include\" >nul
copy "snake_game.h" "%PROJECT_DIR%\Include\" >nul
copy "stm32f4xx_it.h" "%PROJECT_DIR%\Include\" >nul
copy "stm32f4xx_hal_conf.h" "%PROJECT_DIR%\Include\" >nul
echo [成功] 头文件复制完成

:: 复制说明文档
echo.
echo 5. 复制说明文档...
copy "Keil项目文件清单.md" "%PROJECT_DIR%\" >nul
copy "Keil项目创建指南.md" "%PROJECT_DIR%\" >nul
copy "Keil代码适配指南.md" "%PROJECT_DIR%\" >nul
copy "Keil烧录调试指南.md" "%PROJECT_DIR%\" >nul
echo [成功] 说明文档复制完成

:: 创建项目说明文件
echo.
echo 6. 创建项目说明文件...
(
echo # STM32F411CEU6贪吃蛇游戏项目
echo.
echo ## 项目结构
echo ```
echo STM32_Snake_Keil/
echo ├── Project/        ^(Keil项目文件^)
echo ├── Source/         ^(源代码文件^)
echo ├── Include/        ^(头文件^)
echo ├── Output/         ^(编译输出^)
echo └── 说明文档
echo ```
echo.
echo ## 使用方法
echo 1. 启动Keil uVision5
echo 2. 创建新项目：Project → New uVision Project
echo 3. 选择保存位置：Project文件夹
echo 4. 项目名称：Snake_Game
echo 5. 选择芯片：STM32F411CEUx
echo 6. 配置运行时环境：CMSIS-CORE, Device-Startup
echo 7. 添加源文件到项目
echo 8. 配置项目选项
echo 9. 编译项目
echo.
echo ## 详细步骤
echo 请参考项目中的说明文档：
echo - Keil项目文件清单.md
echo - Keil项目创建指南.md
echo - Keil代码适配指南.md
echo - Keil烧录调试指南.md
echo.
echo ## 硬件连接
echo ### LCD显示屏 ^(CL18CG958-18B^)
echo ```
echo STM32F411CEU6    →    LCD
echo PB2              →    RESET
echo PB3              →    SCK
echo PB4              →    DC
echo PB5              →    SDA
echo PA15             →    CS
echo 3.3V             →    VCC
echo GND              →    GND
echo ```
echo.
echo ### 按键连接
echo ```
echo STM32F411CEU6    →    按键
echo PA0              →    上键
echo PA1              →    下键
echo PA2              →    左键
echo PA3              →    右键
echo ```
echo.
echo ## 编译输出
echo 编译成功后，在Output目录中会生成：
echo - Snake_Game.hex ^(烧录文件^)
echo - Snake_Game.axf ^(可执行文件^)
echo - Snake_Game.map ^(内存映射^)
echo.
echo 祝您使用愉快！🐍🎮
) > "%PROJECT_DIR%\README.md"
echo [成功] 项目说明文件创建完成

:: 显示完成信息
echo.
echo ========================================
echo 🎉 Keil项目创建完成！
echo ========================================
echo.
echo 📁 项目位置: %cd%\%PROJECT_DIR%
echo.
echo 📋 项目包含:
echo   ✅ Source/     - 6个源代码文件
echo   ✅ Include/    - 5个头文件
echo   ✅ Project/    - Keil项目目录 ^(空^)
echo   ✅ Output/     - 编译输出目录 ^(空^)
echo   ✅ 说明文档    - 详细使用指南
echo.
echo 🔧 下一步操作:
echo   1. 启动Keil uVision5
echo   2. 打开项目文件夹: %PROJECT_DIR%
echo   3. 按照 "Keil项目文件清单.md" 创建项目
echo   4. 添加源文件并编译
echo.
echo 💡 提示:
echo   - 所有源文件已准备就绪
echo   - 详细步骤请参考说明文档
echo   - 如有问题请查看故障排除部分
echo.

:: 询问是否打开项目文件夹
echo 是否现在打开项目文件夹？ (Y/N)
set /p open_folder=
if /i "%open_folder%"=="Y" (
    explorer "%PROJECT_DIR%"
)

echo.
echo 按任意键退出...
pause >nul
