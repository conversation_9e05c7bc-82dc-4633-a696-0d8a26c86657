# Linux系统开发环境安装指南

## 🎯 安装目标
在Linux系统上安装STM32开发所需的工具链，支持：
- Ubuntu/Debian系统
- CentOS/RHEL系统
- Arch Linux系统

## 🐧 Ubuntu/Debian系统安装

### 第一步：更新系统包管理器
```bash
sudo apt update
sudo apt upgrade -y
```

### 第二步：安装ARM GCC工具链
```bash
# 安装ARM GCC编译器
sudo apt install gcc-arm-none-eabi -y

# 安装相关工具
sudo apt install binutils-arm-none-eabi -y
sudo apt install gdb-arm-none-eabi -y
```

### 第三步：安装构建工具
```bash
# 安装Make和其他构建工具
sudo apt install build-essential -y
sudo apt install make -y
sudo apt install git -y
```

### 第四步：安装调试和烧录工具（可选）
```bash
# 安装OpenOCD
sudo apt install openocd -y

# 安装ST-Link工具
sudo apt install stlink-tools -y
```

### 第五步：验证安装
```bash
# 检查ARM GCC版本
arm-none-eabi-gcc --version

# 检查Make版本
make --version

# 检查OpenOCD版本
openocd --version
```

**预期输出示例：**
```
arm-none-eabi-gcc (15:9-2019-q4-0ubuntu1) 9.2.1 20191025 (release) [ARM/arm-9-branch revision 277599]
Copyright (C) 2019 Free Software Foundation, Inc.
```

## 🎩 CentOS/RHEL系统安装

### 第一步：启用EPEL仓库
```bash
# CentOS 7
sudo yum install epel-release -y

# CentOS 8/RHEL 8
sudo dnf install epel-release -y
```

### 第二步：安装开发工具
```bash
# CentOS 7
sudo yum groupinstall "Development Tools" -y
sudo yum install make git -y

# CentOS 8/RHEL 8
sudo dnf groupinstall "Development Tools" -y
sudo dnf install make git -y
```

### 第三步：手动安装ARM GCC工具链
由于CentOS仓库中可能没有ARM GCC，需要手动安装：

```bash
# 下载ARM GCC工具链
cd /tmp
wget https://developer.arm.com/-/media/Files/downloads/gnu-rm/10.3-2021.10/gcc-arm-none-eabi-10.3-2021.10-x86_64-linux.tar.bz2

# 解压到/opt目录
sudo tar -xjf gcc-arm-none-eabi-10.3-2021.10-x86_64-linux.tar.bz2 -C /opt/

# 创建符号链接
sudo ln -s /opt/gcc-arm-none-eabi-10.3-2021.10 /opt/gcc-arm-none-eabi

# 添加到PATH
echo 'export PATH=/opt/gcc-arm-none-eabi/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

## 🏹 Arch Linux系统安装

### 第一步：更新系统
```bash
sudo pacman -Syu
```

### 第二步：安装ARM工具链
```bash
# 安装ARM GCC工具链
sudo pacman -S arm-none-eabi-gcc
sudo pacman -S arm-none-eabi-binutils
sudo pacman -S arm-none-eabi-gdb

# 安装构建工具
sudo pacman -S base-devel
sudo pacman -S make
sudo pacman -S git
```

### 第三步：安装调试工具（可选）
```bash
# 安装OpenOCD
sudo pacman -S openocd

# 从AUR安装stlink（需要yay或其他AUR助手）
yay -S stlink
```

## 🔧 通用配置步骤

### 配置用户权限（用于ST-Link）
```bash
# 添加用户到dialout组
sudo usermod -a -G dialout $USER

# 创建udev规则文件
sudo tee /etc/udev/rules.d/99-stlink.rules > /dev/null <<EOF
# ST-Link V2
SUBSYSTEM=="usb", ATTR{idVendor}=="0483", ATTR{idProduct}=="3748", MODE="0666"
# ST-Link V2-1
SUBSYSTEM=="usb", ATTR{idVendor}=="0483", ATTR{idProduct}=="374b", MODE="0666"
# ST-Link V3
SUBSYSTEM=="usb", ATTR{idVendor}=="0483", ATTR{idProduct}=="374f", MODE="0666"
EOF

# 重新加载udev规则
sudo udevadm control --reload-rules
sudo udevadm trigger
```

### 创建工作目录
```bash
# 创建项目目录
mkdir -p ~/stm32_projects
cd ~/stm32_projects
```

## ✅ 环境验证

### 完整验证脚本
创建验证脚本：
```bash
cat > check_environment.sh << 'EOF'
#!/bin/bash

echo "=== STM32开发环境检查 ==="
echo

# 检查ARM GCC
echo "1. 检查ARM GCC编译器："
if command -v arm-none-eabi-gcc &> /dev/null; then
    arm-none-eabi-gcc --version | head -1
    echo "✅ ARM GCC已安装"
else
    echo "❌ ARM GCC未安装"
fi
echo

# 检查Make
echo "2. 检查Make工具："
if command -v make &> /dev/null; then
    make --version | head -1
    echo "✅ Make已安装"
else
    echo "❌ Make未安装"
fi
echo

# 检查其他工具
echo "3. 检查其他工具："
for tool in arm-none-eabi-objcopy arm-none-eabi-size arm-none-eabi-objdump; do
    if command -v $tool &> /dev/null; then
        echo "✅ $tool 已安装"
    else
        echo "❌ $tool 未安装"
    fi
done
echo

# 检查可选工具
echo "4. 检查可选工具："
for tool in openocd st-flash git; do
    if command -v $tool &> /dev/null; then
        echo "✅ $tool 已安装"
    else
        echo "⚠️  $tool 未安装（可选）"
    fi
done

echo
echo "=== 检查完成 ==="
EOF

chmod +x check_environment.sh
./check_environment.sh
```

## 🚨 常见问题解决

### 问题1：权限不足
**错误信息**：`Permission denied`
**解决方法**：
```bash
# 确保用户在正确的组中
groups $USER

# 如果没有dialout组，添加用户
sudo usermod -a -G dialout $USER

# 重新登录或重启
```

### 问题2：找不到包
**错误信息**：`Package not found`
**解决方法**：
```bash
# Ubuntu/Debian
sudo apt update
sudo apt search arm-none-eabi

# CentOS/RHEL
sudo yum search arm-none-eabi
# 或手动安装（见上面的步骤）
```

### 问题3：版本过旧
**解决方法**：
```bash
# 添加官方ARM仓库（Ubuntu）
sudo add-apt-repository ppa:team-gcc-arm-embedded/ppa
sudo apt update
sudo apt install gcc-arm-embedded
```

### 问题4：ST-Link无法识别
**解决方法**：
```bash
# 检查设备是否连接
lsusb | grep STMicroelectronics

# 检查udev规则
ls -la /etc/udev/rules.d/*stlink*

# 重新加载规则
sudo udevadm control --reload-rules
sudo udevadm trigger
```

## 📝 推荐编辑器

### Visual Studio Code
```bash
# Ubuntu/Debian
sudo snap install code --classic

# 或者下载deb包
wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > packages.microsoft.gpg
sudo install -o root -g root -m 644 packages.microsoft.gpg /etc/apt/trusted.gpg.d/
sudo sh -c 'echo "deb [arch=amd64,arm64,armhf signed-by=/etc/apt/trusted.gpg.d/packages.microsoft.gpg] https://packages.microsoft.com/repos/code stable main" > /etc/apt/sources.list.d/vscode.list'
sudo apt update
sudo apt install code
```

### Vim/Neovim（轻量级选择）
```bash
# 安装Vim
sudo apt install vim

# 或安装Neovim
sudo apt install neovim
```

## 🎉 安装完成
恭喜！您已经成功在Linux系统上安装了STM32开发环境。
现在可以继续进行项目编译了。

### 下一步
1. 下载项目源代码
2. 进入项目目录
3. 执行编译命令
