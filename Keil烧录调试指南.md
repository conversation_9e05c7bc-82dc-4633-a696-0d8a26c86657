# Keil uVision5 烧录和调试指南

## 🎯 目标
使用Keil uVision5将编译好的STM32贪吃蛇程序烧录到STM32F411CEU6单片机，并进行调试。

## 🔌 第一步：硬件连接准备

### 1.1 ST-Link连接
确保ST-Link调试器正确连接：

**ST-Link V2 连接**：
```
ST-Link    →    STM32F411CEU6
3.3V       →    3.3V (VDD)
GND        →    GND
SWDIO      →    PA13 (SWDIO)
SWCLK      →    PA14 (SWCLK)
NRST       →    NRST (可选)
```

**ST-Link V3 连接**：
```
ST-Link    →    STM32F411CEU6
VDD        →    3.3V
GND        →    GND
SWDIO      →    PA13
SWCLK      →    PA14
```

### 1.2 LCD和按键连接
确保LCD显示屏和按键按照之前的连接图正确连接：

**LCD连接**：
- PB2 → LCD_RESET
- PB3 → LCD_SCK
- PB4 → LCD_DC
- PB5 → LCD_SDA
- PA15 → LCD_CS

**按键连接**：
- PA0 → 上键
- PA1 → 下键
- PA2 → 左键
- PA3 → 右键

### 1.3 电源检查
- 确保STM32板子有稳定的3.3V电源供应
- 检查所有连接线是否牢固
- 确认ST-Link指示灯正常

## 💾 第二步：在Keil中直接烧录

### 2.1 配置调试器
1. 在Keil中打开项目
2. 右键项目名称 → **"Options for Target..."**
3. 切换到 **"Debug"** 选项卡
4. 确保选择了 **"ST-Link Debugger"**
5. 点击 **"Settings"** 按钮

### 2.2 ST-Link设置
在ST-Link设置窗口中：
1. **Port**：选择 **"SW"** (Serial Wire)
2. **Max Clock**：设置为 **"10 MHz"**
3. **Reset**：选择 **"HW Reset"**
4. 点击 **"Flash Download"** 选项卡

### 2.3 Flash下载设置
在Flash Download选项卡中：
1. 勾选 **"Erase Sectors"**
2. 勾选 **"Program"**
3. 勾选 **"Verify"**
4. 勾选 **"Reset and Run"**
5. **Programming Algorithm**：应该自动选择 `STM32F4xx Flash`
6. 点击 **"OK"** 保存设置

### 2.4 开始下载
1. 确保ST-Link已连接到电脑和目标板
2. 点击工具栏上的 **"Download"** 按钮 (📥)
3. 或使用快捷键 **F8**
4. 或菜单：**Flash** → **Download**

### 2.5 下载结果
成功下载后，Output窗口会显示：
```
Load "C:\\...\\Snake_Game.axf"
Erase Done.
Programming Done.
Verify OK.
Flash Load finished at 14:30:25
```

## 🐛 第三步：调试功能

### 3.1 启动调试会话
1. 点击工具栏上的 **"Start/Stop Debug Session"** 按钮 (🐛)
2. 或使用快捷键 **Ctrl+F5**
3. 或菜单：**Debug** → **Start/Stop Debug Session**

### 3.2 调试界面介绍
进入调试模式后，界面会发生变化：
- **代码窗口**：显示当前执行的代码
- **寄存器窗口**：显示CPU寄存器状态
- **内存窗口**：查看内存内容
- **变量窗口**：监视变量值
- **调用栈窗口**：显示函数调用栈

### 3.3 常用调试操作
**断点操作**：
- **设置断点**：在代码行号处单击
- **删除断点**：再次单击断点
- **禁用断点**：右键选择"Disable Breakpoint"

**程序控制**：
- **运行**：F5 或点击 ▶️ 按钮
- **暂停**：点击 ⏸️ 按钮
- **单步执行**：F10 (Step Over)
- **单步进入**：F11 (Step Into)
- **单步跳出**：Shift+F11 (Step Out)
- **重启**：Ctrl+Shift+F5

### 3.4 监视变量
1. 在代码中选择变量名
2. 右键选择 **"Add to Watch"**
3. 在Watch窗口中查看变量值变化

### 3.5 查看内存
1. 菜单：**View** → **Memory Windows** → **Memory 1**
2. 在地址栏输入内存地址（如：0x20000000）
3. 查看RAM内容变化

## 📱 第四步：使用STM32CubeProgrammer烧录

### 4.1 安装STM32CubeProgrammer
1. 访问：https://www.st.com/en/development-tools/stm32cubeprog.html
2. 下载并安装STM32CubeProgrammer
3. 安装完成后启动软件

### 4.2 连接设备
1. 确保ST-Link连接正确
2. 在STM32CubeProgrammer中：
   - 选择连接方式：**ST-LINK**
   - 点击 **"Connect"** 按钮
3. 成功连接后会显示芯片信息

### 4.3 烧录HEX文件
1. 点击 **"Open file"** 按钮
2. 选择编译生成的 `Snake_Game.hex` 文件
3. 文件加载后，点击 **"Download"** 按钮
4. 等待烧录完成

### 4.4 验证烧录
1. 点击 **"Verify"** 按钮验证烧录结果
2. 或点击 **"Read"** 按钮读取Flash内容对比

## 🚨 常见问题解决

### 问题1：无法连接ST-Link
**错误信息**：`No ST-LINK detected`
**解决方法**：
1. 检查ST-Link驱动是否正确安装
2. 更换USB线缆
3. 检查ST-Link硬件连接
4. 重新安装ST-Link驱动

### 问题2：目标芯片无响应
**错误信息**：`Cannot access target`
**解决方法**：
1. 检查SWDIO和SWCLK连接
2. 确认目标板电源正常
3. 尝试降低SWD时钟频率
4. 检查NRST连接

### 问题3：Flash写保护
**错误信息**：`Flash write protected`
**解决方法**：
1. 在STM32CubeProgrammer中解除写保护
2. 菜单：**Option Bytes** → 修改保护设置
3. 或在Keil中设置Flash算法

### 问题4：程序不运行
**可能原因**：
1. 时钟配置错误
2. 向量表位置错误
3. 堆栈设置问题

**解决方法**：
1. 检查SystemClock_Config()函数
2. 验证启动文件配置
3. 确认内存映射设置

### 问题5：LCD无显示
**检查步骤**：
1. 用万用表检查LCD电源（3.3V）
2. 验证SPI连接线
3. 检查复位信号
4. 在调试中单步执行LCD初始化代码

## 📊 第五步：性能监控

### 5.1 CPU使用率监控
在调试模式下：
1. 菜单：**View** → **Analysis Windows** → **Performance Analyzer**
2. 监控CPU使用率和函数执行时间

### 5.2 内存使用监控
1. 查看RAM使用情况
2. 监控堆栈使用
3. 检查内存泄漏

### 5.3 实时变量监控
1. 添加关键变量到Watch窗口
2. 监控游戏状态变化
3. 观察蛇的移动和碰撞检测

## ✅ 调试成功检查清单

程序正常运行时应该看到：
- [ ] LCD显示正常初始化
- [ ] 游戏界面正确显示
- [ ] 蛇能够正常移动
- [ ] 按键响应正常
- [ ] 食物能够正常生成
- [ ] 得分显示正确
- [ ] 碰撞检测工作正常

## 🎉 烧录调试完成

恭喜！您已经成功完成了STM32贪吃蛇项目的烧录和调试。
现在可以享受您亲手制作的贪吃蛇游戏了！

## 🔄 后续优化建议

1. **性能优化**：分析代码执行效率
2. **功能扩展**：添加音效、动画等
3. **用户体验**：优化游戏平衡性
4. **代码重构**：提高代码可维护性
