/*
******************************************************************************
**
** @file        : STM32F411CEUx_FLASH.ld
**
** <AUTHOR> AI Assistant
**
** @brief       : STM32F411CEUx设备的链接脚本
**                用于GNU ld链接器
**
******************************************************************************
** @attention
**
** 此文件定义了STM32F411CEUx设备的内存布局
** Flash: 512KB (0x08000000 - 0x0807FFFF)
** RAM:   128KB (0x20000000 - 0x2001FFFF)
**
******************************************************************************
*/

/* 程序入口点 */
ENTRY(Reset_Handler)

/* 指定最小堆栈大小 */
_Min_Heap_Size = 0x200;      /* 需要的最小堆大小 */
_Min_Stack_Size = 0x400;     /* 需要的最小栈大小 */

/* 指定内存区域 */
MEMORY
{
RAM (xrw)      : ORIGIN = 0x20000000, LENGTH = 128K
FLASH (rx)     : ORIGIN = 0x8000000, LENGTH = 512K
}

/* 定义输出段 */
SECTIONS
{
  /* 中断向量表位于Flash的开始 */
  .isr_vector :
  {
    . = ALIGN(4);
    KEEP(*(.isr_vector)) /* 启动代码 */
    . = ALIGN(4);
  } >FLASH

  /* 程序代码和其他只读数据位于Flash */
  .text :
  {
    . = ALIGN(4);
    *(.text)           /* .text段 */
    *(.text*)          /* .text*段 */
    *(.glue_7)         /* ARM到Thumb的胶水代码 */
    *(.glue_7t)        /* Thumb到ARM的胶水代码 */
    *(.eh_frame)

    KEEP (*(.init))
    KEEP (*(.fini))

    . = ALIGN(4);
    _etext = .;        /* 定义全局符号在.text段结束 */
  } >FLASH

  /* 常量数据位于Flash */
  .rodata :
  {
    . = ALIGN(4);
    *(.rodata)         /* .rodata段 */
    *(.rodata*)        /* .rodata*段 */
    . = ALIGN(4);
  } >FLASH

  .ARM.extab   : { *(.ARM.extab* .gnu.linkonce.armextab.*) } >FLASH
  .ARM : {
    __exidx_start = .;
    *(.ARM.exidx*)
    __exidx_end = .;
  } >FLASH

  .preinit_array     :
  {
    PROVIDE_HIDDEN (__preinit_array_start = .);
    KEEP (*(.preinit_array*))
    PROVIDE_HIDDEN (__preinit_array_end = .);
  } >FLASH
  .init_array :
  {
    PROVIDE_HIDDEN (__init_array_start = .);
    KEEP (*(SORT(.init_array.*)))
    KEEP (*(.init_array*))
    PROVIDE_HIDDEN (__init_array_end = .);
  } >FLASH
  .fini_array :
  {
    PROVIDE_HIDDEN (__fini_array_start = .);
    KEEP (*(SORT(.fini_array.*)))
    KEEP (*(.fini_array*))
    PROVIDE_HIDDEN (__fini_array_end = .);
  } >FLASH

  /* 用于初始化数据的LMA */
  _sidata = LOADADDR(.data);

  /* 初始化数据位于RAM但在Flash中加载 */
  .data : 
  {
    . = ALIGN(4);
    _sdata = .;        /* 创建全局符号在数据段开始 */
    *(.data)           /* .data段 */
    *(.data*)          /* .data*段 */

    . = ALIGN(4);
    _edata = .;        /* 定义全局符号在数据段结束 */
  } >RAM AT> FLASH

  
  /* 未初始化数据段 */
  . = ALIGN(4);
  .bss :
  {
    /* 这被链接器使用，用于定义bss段的开始 */
    _sbss = .;         /* 定义全局符号在bss段开始 */
    __bss_start__ = _sbss;
    *(.bss)
    *(.bss*)
    *(COMMON)

    . = ALIGN(4);
    _ebss = .;         /* 定义全局符号在bss段结束 */
    __bss_end__ = _ebss;
  } >RAM

  /* 用户_heap_stack段，用于检查RAM中是否有足够的空间 */
  /* 用于堆+栈 */
  ._user_heap_stack :
  {
    . = ALIGN(8);
    PROVIDE ( end = . );
    PROVIDE ( _end = . );
    . = . + _Min_Heap_Size;
    . = . + _Min_Stack_Size;
    . = ALIGN(8);
  } >RAM

  

  /* 移除调试信息 */
  /DISCARD/ :
  {
    libc.a ( * )
    libm.a ( * )
    libgcc.a ( * )
  }

  .ARM.attributes 0 : { *(.ARM.attributes) }
}


/* 定义堆栈顶部 */
_estack = ORIGIN(RAM) + LENGTH(RAM);    /* 堆栈顶部结束 */
