# ARM GCC调试环境配置指南

## 🎯 调试环境概览

### 调试工具链组成
```
硬件调试器 (ST-Link) 
    ↓
OpenOCD (调试服务器)
    ↓
GDB (调试客户端)
    ↓
IDE/编辑器 (VS Code/命令行)
```

## 🔧 第一步：OpenOCD安装配置

### 1.1 Windows安装OpenOCD

#### 方法1：使用预编译版本（推荐）
```powershell
# 1. 下载OpenOCD
# 访问: https://github.com/xpack-dev-tools/openocd-xpack/releases
# 下载: xpack-openocd-0.11.0-4-win32-x64.zip

# 2. 解压到程序目录
$openocdPath = "C:\Tools\openocd"
# 解压到该目录

# 3. 添加到PATH
$env:PATH += ";$openocdPath\bin"

# 4. 验证安装
openocd --version
```

#### 方法2：使用包管理器
```powershell
# 使用Chocolatey安装
choco install openocd

# 或使用Scoop
scoop install openocd
```

### 1.2 OpenOCD配置文件

#### 创建STM32F411配置文件
```tcl
# 文件: stm32f411_config.cfg

# 调试器配置
source [find interface/stlink.cfg]

# 传输协议
transport select hla_swd

# 目标芯片配置
source [find target/stm32f4x.cfg]

# 工作区域配置
$_TARGETNAME configure -work-area-phys 0x20000000 -work-area-size 0x8000 -work-area-backup 0

# Flash配置
set FLASH_SIZE 0x80000
flash bank $_FLASHNAME stm32f2x 0x08000000 $FLASH_SIZE 0 0 $_TARGETNAME

# 复位配置
reset_config srst_only srst_nogate

# 调试配置
$_TARGETNAME configure -event reset-init {
    # 配置系统时钟等初始化代码
    mww 0x40023C00 0x00000102  # RCC_APB1ENR
}

# 服务器配置
gdb_port 3333
telnet_port 4444
tcl_port 6666
```

### 1.3 测试OpenOCD连接
```powershell
# 启动OpenOCD服务器
openocd -f stm32f411_config.cfg

# 预期输出
# Open On-Chip Debugger 0.11.0
# Licensed under GNU GPL v2
# Info : The selected transport took over low-level target control.
# Info : clock speed 2000 kHz
# Info : STLINK V2 JTAG V17 API V2 SWIM V4 VID 0x0483 PID 0x3748
# Info : using stlink api v2
# Info : Target voltage: 3.240000
# Info : stm32f4x.cpu: hardware has 6 breakpoints, 4 watchpoints
```

## 🐛 第二步：GDB调试器配置

### 2.1 GDB基础命令学习

#### 连接和基本操作
```bash
# 启动GDB
arm-none-eabi-gdb build/snake_game.elf

# GDB命令
(gdb) target remote localhost:3333    # 连接到OpenOCD
(gdb) monitor reset halt              # 复位并停止
(gdb) load                           # 加载程序到Flash
(gdb) monitor reset init             # 复位并初始化
(gdb) continue                       # 开始运行

# 断点操作
(gdb) break main                     # 在main函数设置断点
(gdb) break lcd_driver.c:123         # 在指定文件行设置断点
(gdb) info breakpoints              # 查看断点
(gdb) delete 1                      # 删除断点1

# 程序控制
(gdb) step                          # 单步执行（进入函数）
(gdb) next                          # 单步执行（跳过函数）
(gdb) finish                        # 执行到函数返回
(gdb) continue                      # 继续执行

# 变量查看
(gdb) print variable_name           # 打印变量值
(gdb) print/x variable_name         # 以十六进制打印
(gdb) display variable_name         # 自动显示变量
(gdb) info locals                   # 显示局部变量
(gdb) info registers                # 显示寄存器

# 内存查看
(gdb) x/10x 0x20000000             # 查看内存（十六进制）
(gdb) x/10i $pc                    # 查看指令
```

### 2.2 GDB配置文件

#### 创建.gdbinit文件
```bash
# 文件: .gdbinit

# 连接设置
set confirm off
set pagination off

# 目标连接
define connect
    target remote localhost:3333
    monitor reset halt
end

# 加载和运行
define reload
    monitor reset halt
    load
    monitor reset init
end

# 快速重启
define restart
    monitor reset halt
    monitor reset init
    continue
end

# 显示设置
set print pretty on
set print array on
set print array-indexes on

# 自动加载符号
set auto-load safe-path /

# 历史记录
set history save on
set history size 1000
set history filename ~/.gdb_history

# 启动时自动连接
connect
```

### 2.3 调试脚本示例

#### 自动化调试脚本
```bash
#!/bin/bash
# 文件: debug.sh

# 启动OpenOCD（后台运行）
echo "启动OpenOCD服务器..."
openocd -f stm32f411_config.cfg &
OPENOCD_PID=$!

# 等待OpenOCD启动
sleep 2

# 启动GDB调试
echo "启动GDB调试器..."
arm-none-eabi-gdb build/snake_game.elf \
    -ex "target remote localhost:3333" \
    -ex "monitor reset halt" \
    -ex "load" \
    -ex "monitor reset init" \
    -ex "break main" \
    -ex "continue"

# 清理
echo "清理进程..."
kill $OPENOCD_PID
```

## 🖥️ 第三步：VS Code集成调试

### 3.1 安装必要插件
```json
// VS Code插件列表
{
    "recommendations": [
        "ms-vscode.cpptools",           // C/C++支持
        "ms-vscode.cpptools-extension-pack",
        "marus25.cortex-debug",         // ARM Cortex调试
        "dan-c-underwood.arm",          // ARM汇编支持
        "ms-vscode.makefile-tools"      // Makefile支持
    ]
}
```

### 3.2 配置launch.json
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "STM32 Debug",
            "type": "cortex-debug",
            "request": "launch",
            "servertype": "openocd",
            "cwd": "${workspaceRoot}",
            "executable": "${workspaceRoot}/build/snake_game.elf",
            "configFiles": [
                "stm32f411_config.cfg"
            ],
            "svdFile": "${workspaceRoot}/STM32F411.svd",
            "runToMain": true,
            "showDevDebugOutput": false,
            "preLaunchTask": "build"
        },
        {
            "name": "STM32 Attach",
            "type": "cortex-debug",
            "request": "attach",
            "servertype": "openocd",
            "cwd": "${workspaceRoot}",
            "executable": "${workspaceRoot}/build/snake_game.elf",
            "configFiles": [
                "stm32f411_config.cfg"
            ]
        }
    ]
}
```

### 3.3 配置tasks.json
```json
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "build",
            "type": "shell",
            "command": "make",
            "args": ["all"],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            },
            "problemMatcher": {
                "owner": "cpp",
                "fileLocation": ["relative", "${workspaceRoot}"],
                "pattern": {
                    "regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$",
                    "file": 1,
                    "line": 2,
                    "column": 3,
                    "severity": 4,
                    "message": 5
                }
            }
        },
        {
            "label": "clean",
            "type": "shell",
            "command": "make",
            "args": ["clean"],
            "group": "build"
        },
        {
            "label": "flash",
            "type": "shell",
            "command": "openocd",
            "args": [
                "-f", "stm32f411_config.cfg",
                "-c", "program build/snake_game.elf verify reset exit"
            ],
            "group": "build",
            "dependsOn": "build"
        }
    ]
}
```

## 🔍 第四步：常见调试技巧

### 4.1 硬件故障排查

#### ST-Link连接问题
```powershell
# 检查ST-Link驱动
# 设备管理器中查看"通用串行总线设备"

# 测试ST-Link连接
st-info --probe

# 预期输出
# Found 1 stlink programmers
# serial: 066DFF485750717867114355
# openocd: "\x06\x6d\xff\x48\x57\x50\x71\x78\x67\x11\x43\x55"
# flash: 524288 (pagesize: 16384)
# sram: 131072
# chipid: 0x0431
# descr: F411RE
```

#### 目标板连接检查
```bash
# OpenOCD连接测试
openocd -f interface/stlink.cfg -f target/stm32f4x.cfg -c "init; reset halt; exit"

# 如果连接失败，检查：
# 1. 电源连接（3.3V）
# 2. SWDIO/SWCLK连接
# 3. 复位信号连接
# 4. 接地连接
```

### 4.2 软件调试技巧

#### 断点策略
```c
// 在关键函数设置断点
void LCD_Init(void) {
    // 在函数入口设置断点
    __asm__("nop");  // 调试标记
    
    // 关键步骤设置断点
    LCD_GPIO_Init();
    __asm__("nop");  // 调试标记
    
    LCD_Reset();
    __asm__("nop");  // 调试标记
}
```

#### 变量监视
```bash
# GDB监视变量
(gdb) watch game.snake.length        # 监视蛇长度变化
(gdb) watch game.score               # 监视得分变化
(gdb) rwatch game.food.position      # 监视食物位置读取
```

#### 内存分析
```bash
# 查看堆栈使用
(gdb) info stack
(gdb) backtrace

# 查看内存布局
(gdb) info mem

# 查看特定内存区域
(gdb) x/100x 0x20000000              # 查看RAM开始100字节
(gdb) x/10i $pc                      # 查看当前指令
```

### 4.3 性能分析

#### 执行时间测量
```c
// 在代码中添加性能测量
uint32_t start_time = HAL_GetTick();
Game_Update();  // 要测量的函数
uint32_t end_time = HAL_GetTick();
uint32_t execution_time = end_time - start_time;
```

#### GDB性能分析
```bash
# 设置性能断点
(gdb) break Game_Update
(gdb) commands
> silent
> printf "Game_Update called at %d\n", HAL_GetTick()
> continue
> end
```

## 🚨 第五步：常见问题解决

### 5.1 编译错误排查

#### 链接错误
```bash
# 错误: undefined reference to 'HAL_GPIO_Init'
# 解决: 检查HAL库链接

# 错误: region 'FLASH' overflowed
# 解决: 检查链接脚本中的内存配置

# 错误: multiple definition of 'main'
# 解决: 检查是否有重复的main函数定义
```

#### 包含路径错误
```bash
# 错误: fatal error: 'stm32f4xx_hal.h' file not found
# 解决: 检查Makefile中的包含路径设置

# 在Makefile中添加
C_INCLUDES += -IDrivers/STM32F4xx_HAL_Driver/Inc
```

### 5.2 运行时错误排查

#### HardFault处理
```c
// 添加HardFault处理函数
void HardFault_Handler(void) {
    // 在此设置断点进行调试
    __asm__("BKPT #0");
    while(1) {
        // 无限循环，便于调试
    }
}
```

#### 堆栈溢出检测
```c
// 堆栈溢出检测
extern uint32_t _estack;
extern uint32_t _Min_Stack_Size;

void check_stack_usage(void) {
    uint32_t stack_ptr;
    __asm__("mov %0, sp" : "=r" (stack_ptr));
    
    uint32_t stack_used = (uint32_t)&_estack - stack_ptr;
    if(stack_used > ((uint32_t)&_Min_Stack_Size * 0.8)) {
        // 堆栈使用超过80%，警告
        __asm__("BKPT #1");
    }
}
```

## 📚 调试学习资源

### 在线资源
1. **OpenOCD官方文档**: http://openocd.org/doc/
2. **GDB用户手册**: https://sourceware.org/gdb/documentation/
3. **ARM Cortex-M调试指南**: ARM官方文档
4. **STM32调试技巧**: ST官方应用笔记

### 实践项目
1. 简单的LED闪烁程序调试
2. UART通信程序调试
3. 中断处理程序调试
4. 复杂状态机程序调试

掌握这些调试技能后，您就能够高效地开发和调试STM32项目了！
