@echo off
chcp 65001 >nul
echo ========================================
echo STM32F411CEU6贪吃蛇项目自动编译脚本
echo ========================================
echo.

:: 检查当前目录是否为项目根目录
if not exist "Makefile" (
    echo [错误] 未找到Makefile文件
    echo 请确保在项目根目录运行此脚本
    echo 项目目录应包含: Makefile, src/, inc/ 等文件夹
    pause
    exit /b 1
)

:: 检查ARM GCC工具链
echo 1. 检查ARM GCC工具链...
arm-none-eabi-gcc --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] ARM GCC工具链未找到
    echo 请先安装ARM GCC工具链并添加到PATH环境变量
    pause
    exit /b 1
)
echo [成功] ARM GCC工具链已安装

:: 检查Make工具
echo.
echo 2. 检查Make构建工具...
make --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Make工具未找到
    echo 请先安装Make工具并添加到PATH环境变量
    pause
    exit /b 1
)
echo [成功] Make工具已安装

:: 检查源文件
echo.
echo 3. 检查源文件...
set missing_files=0

if not exist "src\main.c" (
    echo [错误] 缺少文件: src\main.c
    set missing_files=1
)

if not exist "src\lcd_driver.c" (
    echo [错误] 缺少文件: src\lcd_driver.c
    set missing_files=1
)

if not exist "src\snake_game.c" (
    echo [错误] 缺少文件: src\snake_game.c
    set missing_files=1
)

if not exist "src\stm32f4xx_it.c" (
    echo [错误] 缺少文件: src\stm32f4xx_it.c
    set missing_files=1
)

if not exist "src\system_stm32f4xx.c" (
    echo [错误] 缺少文件: src\system_stm32f4xx.c
    set missing_files=1
)

if not exist "src\startup_stm32f411xe.s" (
    echo [错误] 缺少文件: src\startup_stm32f411xe.s
    set missing_files=1
)

if %missing_files% equ 1 (
    echo.
    echo 请确保所有源文件都在src目录中
    pause
    exit /b 1
)
echo [成功] 所有源文件检查完毕

:: 检查头文件
echo.
echo 4. 检查头文件...
if not exist "inc\main.h" (
    echo [警告] 缺少文件: inc\main.h
)

if not exist "inc\lcd_driver.h" (
    echo [警告] 缺少文件: inc\lcd_driver.h
)

if not exist "inc\snake_game.h" (
    echo [警告] 缺少文件: inc\snake_game.h
)

if not exist "inc\stm32f4xx_hal_conf.h" (
    echo [警告] 缺少文件: inc\stm32f4xx_hal_conf.h
    echo 建议使用简化版本的HAL配置文件
)

:: 清理之前的编译文件
echo.
echo 5. 清理之前的编译文件...
if exist "build" (
    rmdir /s /q build
    echo [成功] 已清理build目录
) else (
    echo [信息] build目录不存在，跳过清理
)

:: 开始编译
echo.
echo 6. 开始编译项目...
echo ----------------------------------------
make all
set compile_result=%errorlevel%

echo ----------------------------------------
echo.

:: 检查编译结果
if %compile_result% equ 0 (
    echo ✅ 编译成功！
    echo.
    echo 生成的文件:
    if exist "build\snake_game.elf" (
        echo   ✅ build\snake_game.elf - ELF可执行文件
    )
    if exist "build\snake_game.hex" (
        echo   ✅ build\snake_game.hex - HEX烧录文件 ^(主要文件^)
    )
    if exist "build\snake_game.bin" (
        echo   ✅ build\snake_game.bin - 二进制文件
    )
    if exist "build\snake_game.map" (
        echo   ✅ build\snake_game.map - 内存映射文件
    )
    
    echo.
    echo 程序大小信息:
    if exist "build\snake_game.elf" (
        arm-none-eabi-size build\snake_game.elf
    )
    
    echo.
    echo ========================================
    echo 🎉 编译完成！
    echo ========================================
    echo.
    echo 📁 HEX文件位置: build\snake_game.hex
    echo 📏 文件大小: 
    if exist "build\snake_game.hex" (
        for %%A in (build\snake_game.hex) do echo    %%~zA 字节
    )
    echo.
    echo 💡 下一步:
    echo    1. 使用ST-Link连接STM32开发板
    echo    2. 使用STM32CubeProgrammer烧录HEX文件
    echo    3. 或使用OpenOCD进行烧录和调试
    echo.
    
) else (
    echo ❌ 编译失败！
    echo.
    echo 🔍 常见问题排查:
    echo    1. 检查所有源文件是否存在
    echo    2. 检查ARM GCC工具链是否正确安装
    echo    3. 检查Make工具是否正确安装
    echo    4. 查看上面的错误信息进行具体分析
    echo.
    echo 💡 建议:
    echo    1. 确保使用简化版本的HAL配置文件
    echo    2. 检查Makefile中的路径设置
    echo    3. 确认所有依赖文件都已创建
    echo.
)

echo 按任意键退出...
pause >nul
