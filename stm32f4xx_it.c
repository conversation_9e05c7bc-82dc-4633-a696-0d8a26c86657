/**
 * @file stm32f4xx_it.c
 * @brief 中断服务程序
 * <AUTHOR> Assistant
 * @date 2024
 */

#include "main.h"
#include "stm32f4xx_it.h"

/******************************************************************************/
/*           Cortex-M4 处理器中断和异常处理程序                              */
/******************************************************************************/

/**
 * @brief 此函数处理不可屏蔽中断
 */
void NMI_Handler(void)
{
    /* 用户代码开始 NMI */
    
    /* 用户代码结束 NMI */
}

/**
 * @brief 此函数处理硬件错误中断
 */
void HardFault_Handler(void)
{
    /* 用户代码开始 HardFault_IRQn */
    
    /* 进入无限循环 */
    while(1)
    {
    }
    /* 用户代码结束 HardFault_IRQn */
}

/**
 * @brief 此函数处理内存管理错误中断
 */
void MemManage_Handler(void)
{
    /* 用户代码开始 MemoryManagement_IRQn */
    
    /* 进入无限循环 */
    while(1)
    {
    }
    /* 用户代码结束 MemoryManagement_IRQn */
}

/**
 * @brief 此函数处理预取错误，内存访问错误中断
 */
void BusFault_Handler(void)
{
    /* 用户代码开始 BusFault_IRQn */
    
    /* 进入无限循环 */
    while(1)
    {
    }
    /* 用户代码结束 BusFault_IRQn */
}

/**
 * @brief 此函数处理未定义指令或非法状态中断
 */
void UsageFault_Handler(void)
{
    /* 用户代码开始 UsageFault_IRQn */
    
    /* 进入无限循环 */
    while(1)
    {
    }
    /* 用户代码结束 UsageFault_IRQn */
}

/**
 * @brief 此函数处理系统服务调用通过SWI指令
 */
void SVC_Handler(void)
{
    /* 用户代码开始 SVCall_IRQn */
    
    /* 用户代码结束 SVCall_IRQn */
}

/**
 * @brief 此函数处理调试监视器中断
 */
void DebugMon_Handler(void)
{
    /* 用户代码开始 DebugMonitor_IRQn */
    
    /* 用户代码结束 DebugMonitor_IRQn */
}

/**
 * @brief 此函数处理可挂起的系统服务请求
 */
void PendSV_Handler(void)
{
    /* 用户代码开始 PendSV_IRQn */
    
    /* 用户代码结束 PendSV_IRQn */
}

/**
 * @brief 此函数处理系统滴答定时器中断
 */
void SysTick_Handler(void)
{
    /* 用户代码开始 SysTick_IRQn */
    HAL_IncTick();
    /* 用户代码结束 SysTick_IRQn */
}

/******************************************************************************/
/* STM32F4xx 外设中断处理程序                                                */
/* 如果不使用中断，请在stm32f4xx_it.h文件中注释掉相应的IRQn行               */
/******************************************************************************/
