/**
 * @file stm32f4xx_hal_conf.h
 * @brief HAL配置文件
 * <AUTHOR> Assistant
 * @date 2024
 */

#ifndef __STM32F4xx_HAL_CONF_H
#define __STM32F4xx_HAL_CONF_H

#ifdef __cplusplus
extern "C" {
#endif

/* 导出的类型 */

/* 导出的常量 */

/* ########################## 模块选择 ############################## */
/**
 * @brief 这是HAL模块的列表，用户可以选择启用或禁用
 */
#define HAL_MODULE_ENABLED

#ifdef HAL_MODULE_ENABLED
#define HAL_ADC_MODULE_ENABLED
#define HAL_CORTEX_MODULE_ENABLED
#define HAL_DMA_MODULE_ENABLED
#define HAL_FLASH_MODULE_ENABLED
#define HAL_GPIO_MODULE_ENABLED
#define HAL_PWR_MODULE_ENABLED
#define HAL_RCC_MODULE_ENABLED
#define HAL_SPI_MODULE_ENABLED
#define HAL_TIM_MODULE_ENABLED
#define HAL_UART_MODULE_ENABLED
#endif /* HAL_MODULE_ENABLED */

/* ########################## HSE/HSI 值 ########################### */
/**
 * @brief 调整应用程序中使用的外部高速振荡器(HSE)的值
 * 此值由用户应用程序使用，用于正确配置RCC和Flash控制器
 * 代码中的HSE值必须与板上使用的真实频率相同，否则
 * 此代码将无法正常工作。
 */
#if !defined(HSE_VALUE)
#define HSE_VALUE    25000000U /*!< 外部振荡器的值，单位为Hz */
#endif /* HSE_VALUE */

#if !defined(HSE_STARTUP_TIMEOUT)
#define HSE_STARTUP_TIMEOUT    100U   /*!< HSE启动超时时间，单位为ms */
#endif /* HSE_STARTUP_TIMEOUT */

/**
 * @brief 内部高速振荡器(HSI)值
 * 此值由用户应用程序使用，用于正确配置RCC和Flash控制器
 * 代码中的HSI值必须与硬件中的真实频率相同，否则
 * 此代码将无法正常工作。
 */
#if !defined(HSI_VALUE)
#define HSI_VALUE    16000000U /*!< 内部振荡器的值，单位为Hz */
#endif /* HSI_VALUE */

/**
 * @brief 内部低速振荡器(LSI)值
 */
#if !defined(LSI_VALUE)
#define LSI_VALUE  32000U      /*!< LSI典型值，单位为Hz */
#endif /* LSI_VALUE */

/**
 * @brief 外部低速振荡器(LSE)值
 */
#if !defined(LSE_VALUE)
#define LSE_VALUE  32768U      /*!< LSE典型值，单位为Hz */
#endif /* LSE_VALUE */

/**
 * @brief 外部时钟源用于I2S外设的值
 * 此值由用户应用程序使用，用于正确配置I2S时钟源
 * 代码中的值必须与板上使用的真实频率相同，否则
 * 此代码将无法正常工作。
 */
#if !defined(EXTERNAL_CLOCK_VALUE)
#define EXTERNAL_CLOCK_VALUE    12288000U /*!< 外部时钟的值，单位为Hz */
#endif /* EXTERNAL_CLOCK_VALUE */

/* ########################### 系统配置 ###################### */
/**
 * @brief 这是HAL系统配置部分
 */
#define VDD_VALUE                    3300U /*!< VDD值，单位为mv */
#define TICK_INT_PRIORITY            0U    /*!< tick中断优先级 */
#define USE_RTOS                     0U
#define PREFETCH_ENABLE              1U
#define INSTRUCTION_CACHE_ENABLE     1U
#define DATA_CACHE_ENABLE            1U

/* ########################## 断言选择 ############################## */
/**
 * @brief 取消注释下面的行以扩展"assert_param"宏在HAL驱动程序代码中
 */
/* #define USE_FULL_ASSERT    1U */

/* ################## SPI外设配置 ################################### */

/* CRC功能：在以下SPI HAL驱动程序中使用 */
#define USE_SPI_CRC                     0U

/* 包含文件 */
/**
 * @brief 包含模块的头文件
 */

#ifdef HAL_RCC_MODULE_ENABLED
#include "stm32f4xx_hal_rcc.h"
#endif /* HAL_RCC_MODULE_ENABLED */

#ifdef HAL_GPIO_MODULE_ENABLED
#include "stm32f4xx_hal_gpio.h"
#endif /* HAL_GPIO_MODULE_ENABLED */

#ifdef HAL_DMA_MODULE_ENABLED
#include "stm32f4xx_hal_dma.h"
#endif /* HAL_DMA_MODULE_ENABLED */

#ifdef HAL_CORTEX_MODULE_ENABLED
#include "stm32f4xx_hal_cortex.h"
#endif /* HAL_CORTEX_MODULE_ENABLED */

#ifdef HAL_ADC_MODULE_ENABLED
#include "stm32f4xx_hal_adc.h"
#endif /* HAL_ADC_MODULE_ENABLED */

#ifdef HAL_FLASH_MODULE_ENABLED
#include "stm32f4xx_hal_flash.h"
#endif /* HAL_FLASH_MODULE_ENABLED */

#ifdef HAL_PWR_MODULE_ENABLED
#include "stm32f4xx_hal_pwr.h"
#endif /* HAL_PWR_MODULE_ENABLED */

#ifdef HAL_SPI_MODULE_ENABLED
#include "stm32f4xx_hal_spi.h"
#endif /* HAL_SPI_MODULE_ENABLED */

#ifdef HAL_TIM_MODULE_ENABLED
#include "stm32f4xx_hal_tim.h"
#endif /* HAL_TIM_MODULE_ENABLED */

#ifdef HAL_UART_MODULE_ENABLED
#include "stm32f4xx_hal_uart.h"
#endif /* HAL_UART_MODULE_ENABLED */

/* 导出的宏 */
#ifdef USE_FULL_ASSERT
/**
 * @brief assert_param宏用于函数的参数检查
 * @param expr 如果expr为false，它调用assert_failed函数
 *        该函数包含无效参数的源文件名和行号
 *        如果expr为true，它不返回任何值
 * @retval 无
 */
#define assert_param(expr) ((expr) ? (void)0U : assert_failed((uint8_t *)__FILE__, __LINE__))
/* 导出的函数 */
void assert_failed(uint8_t* file, uint32_t line);
#else
#define assert_param(expr) ((void)0U)
#endif /* USE_FULL_ASSERT */

#ifdef __cplusplus
}
#endif

#endif /* __STM32F4xx_HAL_CONF_H */
