# STM32F411CEU6 贪吃蛇游戏项目配置说明

## 开发环境要求

### 1. 必需的软件工具

#### ARM GCC 工具链
- **arm-none-eabi-gcc** (推荐版本: 10.3.1 或更高)
- **arm-none-eabi-objcopy**
- **arm-none-eabi-size**
- **arm-none-eabi-objdump**

#### 构建工具
- **GNU Make** (版本 4.0 或更高)

#### 调试和烧录工具
- **OpenOCD** (用于调试和烧录)
- **ST-Link** 驱动程序
- **STM32CubeProgrammer** (可选，图形化烧录工具)

### 2. 开发环境安装

#### Windows 系统
1. **安装 ARM GCC 工具链**
   ```
   下载地址: https://developer.arm.com/tools-and-software/open-source-software/developer-tools/gnu-toolchain/gnu-rm
   选择: GNU Arm Embedded Toolchain
   ```

2. **安装 Make 工具**
   ```
   方法1: 安装 MinGW-w64
   方法2: 使用 Chocolatey: choco install make
   方法3: 使用 MSYS2: pacman -S make
   ```

3. **安装 OpenOCD**
   ```
   下载地址: https://github.com/xpack-dev-tools/openocd-xpack/releases
   ```

#### Linux (Ubuntu/Debian) 系统
```bash
# 安装 ARM GCC 工具链
sudo apt update
sudo apt install gcc-arm-none-eabi

# 安装构建工具
sudo apt install build-essential

# 安装 OpenOCD
sudo apt install openocd

# 安装 ST-Link 工具
sudo apt install stlink-tools
```

#### macOS 系统
```bash
# 使用 Homebrew 安装
brew install --cask gcc-arm-embedded
brew install make
brew install openocd
brew install stlink
```

## 项目编译配置

### 1. Makefile 配置说明

#### 编译器路径配置
```makefile
# 如果工具链在系统PATH中，保持默认配置
PREFIX = arm-none-eabi-

# 如果工具链在特定路径，设置GCC_PATH
# GCC_PATH = /path/to/your/gcc-arm-none-eabi/bin
```

#### 优化级别配置
```makefile
# 调试版本 (默认)
OPT = -Og

# 发布版本 (体积优化)
OPT = -Os

# 发布版本 (速度优化)
OPT = -O2
```

#### 调试信息配置
```makefile
# 启用调试信息
DEBUG = 1

# 禁用调试信息 (发布版本)
DEBUG = 0
```

### 2. 编译命令

#### 基本编译
```bash
# 清理之前的编译文件
make clean

# 编译项目
make all

# 或者直接
make
```

#### 调试版本编译
```bash
make clean
make DEBUG=1
```

#### 发布版本编译
```bash
make clean
make DEBUG=0 OPT=-Os
```

### 3. 编译输出文件

编译成功后，在 `build/` 目录下会生成以下文件：
- `snake_game.elf` - ELF 格式可执行文件 (用于调试)
- `snake_game.hex` - Intel HEX 格式文件 (用于烧录)
- `snake_game.bin` - 二进制文件 (用于烧录)
- `snake_game.map` - 内存映射文件 (用于分析)

## 硬件配置

### 1. STM32F411CEU6 引脚配置

#### 系统时钟配置
- **HSI**: 16MHz (内部高速振荡器)
- **PLL**: 配置为 84MHz 系统时钟
- **AHB**: 84MHz
- **APB1**: 42MHz
- **APB2**: 84MHz

#### GPIO 配置
```c
// LCD 控制引脚
PB2  - LCD_RESET (输出, 推挽)
PB3  - LCD_SCK   (输出, 推挽, 高速)
PB4  - LCD_DC    (输出, 推挽)
PB5  - LCD_SDA   (输出, 推挽, 高速)
PA15 - LCD_CS    (输出, 推挽)

// 按键输入引脚
PA0  - KEY_UP    (输入, 上拉)
PA1  - KEY_DOWN  (输入, 上拉)
PA2  - KEY_LEFT  (输入, 上拉)
PA3  - KEY_RIGHT (输入, 上拉)
```

### 2. LCD 显示屏配置

#### CL18CG958-18B 规格
- **尺寸**: 1.8 寸
- **分辨率**: 128 x 160 像素
- **驱动芯片**: ST7735
- **接口**: SPI (4线)
- **颜色**: RGB565 (16位)
- **工作电压**: 3.3V

#### SPI 时序配置
- **时钟极性**: CPOL = 0
- **时钟相位**: CPHA = 0
- **数据位**: 8位
- **MSB优先**
- **软件SPI实现**

## 调试配置

### 1. OpenOCD 配置

#### 创建 openocd.cfg 文件
```
# OpenOCD 配置文件
source [find interface/stlink.cfg]
source [find target/stm32f4x.cfg]

# 设置工作区域
$_TARGETNAME configure -work-area-phys 0x20000000 -work-area-size 0x20000 -work-area-backup 0

# 设置Flash配置
set FLASH_SIZE 0x80000
flash bank $_FLASHNAME stm32f2x 0x08000000 $FLASH_SIZE 0 0 $_TARGETNAME

# 复位配置
reset_config srst_only
```

#### 调试命令
```bash
# 启动 OpenOCD 服务器
openocd -f openocd.cfg

# 在另一个终端启动 GDB
arm-none-eabi-gdb build/snake_game.elf

# 在 GDB 中连接到 OpenOCD
(gdb) target remote localhost:3333
(gdb) monitor reset halt
(gdb) load
(gdb) continue
```

### 2. 烧录配置

#### 使用 OpenOCD 烧录
```bash
# 烧录 HEX 文件
openocd -f openocd.cfg -c "program build/snake_game.hex verify reset exit"

# 烧录 ELF 文件
openocd -f openocd.cfg -c "program build/snake_game.elf verify reset exit"
```

#### 使用 st-flash 烧录
```bash
# 烧录二进制文件
st-flash write build/snake_game.bin 0x8000000

# 擦除Flash
st-flash erase
```

## 常见问题解决

### 1. 编译问题

#### 工具链未找到
```
错误: arm-none-eabi-gcc: command not found
解决: 确保ARM GCC工具链已安装并添加到系统PATH
```

#### 链接错误
```
错误: undefined reference to `__libc_init_array`
解决: 确保使用了正确的启动文件和链接脚本
```

#### 内存不足
```
错误: region `RAM' overflowed
解决: 减少堆栈大小或优化代码以减少RAM使用
```

### 2. 烧录问题

#### ST-Link 连接失败
```
错误: Error: open failed
解决: 
1. 检查ST-Link驱动是否正确安装
2. 确保ST-Link与目标板连接正确
3. 检查目标板电源是否正常
```

#### Flash 写保护
```
错误: Flash write protected
解决: 使用STM32CubeProgrammer解除写保护
```

### 3. 运行问题

#### 程序不运行
```
可能原因:
1. 时钟配置错误
2. 向量表位置错误
3. 堆栈指针设置错误

解决方法:
1. 检查SystemClock_Config()函数
2. 确认链接脚本中的向量表位置
3. 验证启动文件中的堆栈设置
```

#### LCD 无显示
```
可能原因:
1. SPI 连接错误
2. 电源供应问题
3. 初始化序列错误

解决方法:
1. 用万用表检查连接
2. 确认3.3V电源稳定
3. 检查LCD_Init()函数
```

## 性能优化建议

### 1. 代码优化
- 使用 `-Os` 优化标志减少代码大小
- 避免使用浮点运算
- 使用查找表代替复杂计算
- 合理使用内联函数

### 2. 内存优化
- 减少全局变量使用
- 使用位域节省内存
- 优化数据结构对齐
- 合理设置堆栈大小

### 3. 显示优化
- 减少不必要的屏幕刷新
- 使用局部刷新代替全屏刷新
- 优化图形绘制算法
- 使用硬件SPI提高传输速度

## 扩展开发建议

### 1. 添加新功能
- 实现硬件SPI驱动
- 添加音效支持
- 实现数据存储功能
- 添加无线通信

### 2. 移植到其他平台
- 修改GPIO配置
- 调整时钟设置
- 更新链接脚本
- 适配启动文件

### 3. 使用IDE开发
- STM32CubeIDE
- Keil MDK-ARM
- IAR Embedded Workbench
- Visual Studio Code + PlatformIO
