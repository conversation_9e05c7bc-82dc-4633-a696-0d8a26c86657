# STM32F411CEU6贪吃蛇游戏 - Keil uVision5详细操作步骤

## 🎯 操作前准备

### 确认项目文件已准备就绪
项目路径：`C:\Users\<USER>\Desktop\STM32_Snake_Keil`

**目录结构应该是：**
```
STM32_Snake_Keil/
├── Project/        (空目录，准备放置Keil项目文件)
├── Source/         (6个源代码文件)
│   ├── main.c
│   ├── lcd_driver.c
│   ├── snake_game.c
│   ├── stm32f4xx_it.c
│   ├── system_stm32f4xx.c
│   └── startup_stm32f411xe.s
├── Include/        (5个头文件)
│   ├── main.h
│   ├── lcd_driver.h
│   ├── snake_game.h
│   ├── stm32f4xx_it.h
│   └── stm32f4xx_hal_conf.h
└── Output/         (空目录，准备放置编译输出)
```

## 🔧 第一步：启动Keil uVision5并创建项目

### 1.1 启动Keil uVision5
1. 双击桌面上的 **"Keil uVision5"** 图标
2. 等待软件完全加载（约10-15秒）

### 1.2 创建新项目
1. 在菜单栏点击：**Project** → **New uVision Project...**
2. 在弹出的 "Create New Project" 对话框中：
   - **保存位置**：浏览到 `C:\Users\<USER>\Desktop\STM32_Snake_Keil\Project\`
   - **文件名**：输入 `Snake_Game`
   - 点击 **"保存"** 按钮

### 1.3 选择目标设备
在 "Select Device for Target 'Target 1'" 对话框中：
1. 在左侧设备树中，依次展开：
   - **STMicroelectronics**
   - **STM32F4 Series**
   - **STM32F411**
2. 在右侧列表中选择：**STM32F411CEUx**
3. 点击 **"OK"** 按钮

### 1.4 配置运行时环境
在 "Manage Run-Time Environment" 对话框中：
1. 展开 **CMSIS** 节点，勾选：**CORE**
2. 展开 **Device** 节点，勾选：**Startup**
3. 确认右下角显示 "No unresolved dependencies"
4. 点击 **"OK"** 按钮

## 📁 第二步：添加源文件到项目

### 2.1 创建文件组
在项目窗口（左侧）中：
1. 右键点击 **"Target 1"**
2. 选择 **"Add Group..."**
3. 输入组名：**Application**，点击 **"OK"**
4. 重复上述步骤，创建以下组：
   - **Drivers**
   - **System**

### 2.2 添加Application组文件
1. 右键点击 **"Application"** 组
2. 选择 **"Add Existing Files to Group 'Application'..."**
3. 浏览到：`C:\Users\<USER>\Desktop\STM32_Snake_Keil\Source\`
4. 选择以下文件（按住Ctrl键多选）：
   - `main.c`
   - `snake_game.c`
5. 点击 **"Add"**
6. 点击 **"Close"**

### 2.3 添加Drivers组文件
1. 右键点击 **"Drivers"** 组
2. 选择 **"Add Existing Files to Group 'Drivers'..."**
3. 浏览到：`C:\Users\<USER>\Desktop\STM32_Snake_Keil\Source\`
4. 选择文件：`lcd_driver.c`
5. 点击 **"Add"**
6. 点击 **"Close"**

### 2.4 添加System组文件
1. 右键点击 **"System"** 组
2. 选择 **"Add Existing Files to Group 'System'..."**
3. 浏览到：`C:\Users\<USER>\Desktop\STM32_Snake_Keil\Source\`
4. 选择以下文件（按住Ctrl键多选）：
   - `stm32f4xx_it.c`
   - `system_stm32f4xx.c`
5. 点击 **"Add"**
6. 点击 **"Close"**

## ⚙️ 第三步：配置项目选项

### 3.1 打开项目选项
1. 右键点击项目名称 **"Snake_Game"**
2. 选择 **"Options for Target 'Target 1'..."**
3. 或者使用快捷键：**Alt + F7**

### 3.2 配置Target选项卡
在 **"Target"** 选项卡中：
1. **Xtal (MHz)**：设置为 `16.0`（内部HSI时钟）
2. **Memory Areas** 部分：
   - **IROM1**：
     - 勾选 **"Use"**
     - Start: `0x8000000`
     - Size: `0x80000`（512KB Flash）
   - **IRAM1**：
     - 勾选 **"Use"**
     - Start: `0x20000000`
     - Size: `0x20000`（128KB RAM）

### 3.3 配置Output选项卡
在 **"Output"** 选项卡中：
1. **Name of Executable**：保持 `Snake_Game`
2. **Select Folder for Objects**：点击 **"..."** 按钮
   - 浏览到：`C:\Users\<USER>\Desktop\STM32_Snake_Keil\Output\`
   - 点击 **"确定"**
3. **重要**：勾选 **"Create HEX File"** ✅
4. 勾选 **"Browse Information"**

### 3.4 配置Listing选项卡
在 **"Listing"** 选项卡中：
1. **Select Folder for Listings**：点击 **"..."** 按钮
   - 浏览到：`C:\Users\<USER>\Desktop\STM32_Snake_Keil\Output\`
   - 点击 **"确定"**

### 3.5 配置C/C++选项卡
在 **"C/C++"** 选项卡中：

**Include Paths**：
1. 点击 **"Include Paths"** 右侧的 **"..."** 按钮
2. 点击 **"New (Insert)"** 按钮（文件夹图标）
3. 添加以下路径：
   - `C:\Users\<USER>\Desktop\STM32_Snake_Keil\Include`
   - `C:\Users\<USER>\Desktop\STM32_Snake_Keil\Source`
4. 点击 **"OK"**

**Define**：
1. 在 **"Define"** 文本框中输入：
   ```
   USE_HAL_DRIVER,STM32F411xE
   ```

**Optimization**：
1. 选择 **"Level 1 (-O1)"** 或 **"Level 2 (-O2)"**

### 3.6 配置Debug选项卡
在 **"Debug"** 选项卡中：
1. **Use**：选择 **"ST-Link Debugger"**
2. 点击 **"Settings"** 按钮
3. 在弹出窗口中：
   - **Port**：选择 **"SW"**（Serial Wire）
   - **Max Clock**：设置为 **"10 MHz"**
   - 点击 **"OK"**

### 3.7 保存配置
点击 **"OK"** 保存所有项目配置

## 🔨 第四步：编译项目

### 4.1 开始编译
1. 点击工具栏上的 **"Build"** 按钮（🔨图标）
2. 或者使用快捷键：**F7**
3. 或者菜单：**Project** → **Build Target**

### 4.2 观察编译过程
在底部的 **"Build Output"** 窗口中观察编译进度：

**正常编译过程应显示：**
```
Build started: Project: Snake_Game
*** Using Compiler 'V6.16', folder: 'C:\Keil_v5\ARM\ARMCLANG\Bin'
Build target 'Target 1'
compiling main.c...
compiling snake_game.c...
compiling lcd_driver.c...
compiling stm32f4xx_it.c...
compiling system_stm32f4xx.c...
assembling startup_stm32f411xe.s...
linking...
Program Size: Code=15234 RO-data=1456 RW-data=128 ZI-data=2048
"Snake_Game.axf" - 0 Error(s), 0 Warning(s).
Build Time Elapsed:  00:00:05
```

### 4.3 编译成功标志
看到以下信息表示编译成功：
- **"0 Error(s), 0 Warning(s)"**
- **Program Size** 信息显示
- **Build Time Elapsed** 显示编译时间

## ✅ 第五步：验证编译结果

### 5.1 检查生成的文件
编译成功后，在 `C:\Users\<USER>\Desktop\STM32_Snake_Keil\Output\` 目录中应该有：

**主要文件：**
- **Snake_Game.hex** - HEX烧录文件（主要文件）
- **Snake_Game.axf** - ELF可执行文件
- **Snake_Game.map** - 内存映射文件

**其他文件：**
- *.o - 目标文件
- *.d - 依赖文件
- *.lst - 汇编列表文件

### 5.2 验证HEX文件
1. **文件大小检查**：
   - Snake_Game.hex 文件大小应该在 30-60KB 范围内
   - 如果文件过小（<10KB）或过大（>100KB），可能有问题

2. **文件内容检查**：
   - 用记事本打开 Snake_Game.hex
   - 应该看到以 `:` 开头的十六进制行
   - 第一行通常是：`:020000040800F2`
   - 最后一行应该是：`:00000001FF`

### 5.3 程序大小分析
在编译输出中查看程序大小信息：
```
Program Size: Code=15234 RO-data=1456 RW-data=128 ZI-data=2048
```

**解释：**
- **Code**: 程序代码大小（约15KB）
- **RO-data**: 只读数据大小（约1.4KB）
- **RW-data**: 可读写数据大小（约128字节）
- **ZI-data**: 零初始化数据大小（约2KB）

**总Flash使用**: Code + RO-data + RW-data ≈ 16.8KB
**总RAM使用**: RW-data + ZI-data ≈ 2.2KB

这些数值都在STM32F411CEU6的规格范围内（512KB Flash, 128KB RAM）。

## 🎉 完成！

如果您看到了 **"0 Error(s), 0 Warning(s)"** 的编译结果，并且在Output目录中生成了HEX文件，恭喜您！

**您已经成功编译了STM32F411CEU6贪吃蛇游戏项目！**

**生成的HEX文件位置**：
`C:\Users\<USER>\Desktop\STM32_Snake_Keil\Output\Snake_Game.hex`

这个文件就可以直接烧录到STM32F411CEU6单片机了！
