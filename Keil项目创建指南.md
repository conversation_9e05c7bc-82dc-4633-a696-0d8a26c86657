# Keil uVision5 STM32贪吃蛇项目创建指南

## 🎯 项目创建目标
在Keil uVision5中创建STM32F411CEU6贪吃蛇游戏项目，并导入所有源代码文件。

## 📁 第一步：准备项目文件

### 1.1 创建项目文件夹
1. 在桌面或其他位置创建文件夹：`STM32_Snake_Game_Keil`
2. 在该文件夹内创建子文件夹：
   ```
   STM32_Snake_Game_Keil/
   ├── Source/          (源代码文件)
   ├── Include/         (头文件)
   ├── Project/         (Keil项目文件)
   └── Output/          (编译输出)
   ```

### 1.2 整理源代码文件
将之前的源代码文件按类型分类：

**复制到 Source/ 文件夹：**
- `main.c`
- `lcd_driver.c`
- `snake_game.c`
- `stm32f4xx_it.c`
- `system_stm32f4xx.c`

**复制到 Include/ 文件夹：**
- `main.h`
- `lcd_driver.h`
- `snake_game.h`
- `stm32f4xx_it.h`
- `stm32f4xx_hal_conf.h`

**注意**：`startup_stm32f411xe.s` 和 `STM32F411CEUx_FLASH.ld` 在Keil中不需要，Keil会自动处理。

## 🏗️ 第二步：创建Keil项目

### 2.1 启动Keil uVision5
1. 双击桌面上的 "Keil uVision5" 图标
2. 等待软件完全加载

### 2.2 创建新项目
1. 菜单：**Project** → **New uVision Project...**
2. 浏览到 `STM32_Snake_Game_Keil/Project/` 文件夹
3. 输入项目名称：`Snake_Game`
4. 点击 **"保存"**

### 2.3 选择目标设备
在 "Select Device for Target" 对话框中：
1. 展开 **"STMicroelectronics"**
2. 展开 **"STM32F4 Series"**
3. 展开 **"STM32F411"**
4. 选择 **"STM32F411CEUx"**
5. 点击 **"OK"**

### 2.4 配置运行时环境
在 "Manage Run-Time Environment" 对话框中：

**必需组件：**
1. **CMSIS** → 勾选 **"CORE"**
2. **Device** → 勾选 **"Startup"**
3. **Device** → 勾选 **"STM32Cube HAL"** → 勾选以下子项：
   - **"Common"**
   - **"Cortex"**
   - **"GPIO"**
   - **"PWR"**
   - **"RCC"**

4. 点击 **"OK"**

## 📂 第三步：添加源代码文件

### 3.1 创建文件组
在项目窗口中右键点击 "Target 1"：
1. 选择 **"Add Group..."**
2. 创建以下组：
   - `Application` (应用程序代码)
   - `Drivers` (驱动程序代码)
   - `System` (系统代码)

### 3.2 添加应用程序文件
右键点击 "Application" 组：
1. 选择 **"Add Existing Files to Group 'Application'..."**
2. 浏览到 `Source/` 文件夹
3. 选择以下文件：
   - `main.c`
   - `snake_game.c`
4. 点击 **"Add"**，然后 **"Close"**

### 3.3 添加驱动程序文件
右键点击 "Drivers" 组：
1. 选择 **"Add Existing Files to Group 'Drivers'..."**
2. 选择 `lcd_driver.c`
3. 点击 **"Add"**，然后 **"Close"**

### 3.4 添加系统文件
右键点击 "System" 组：
1. 选择 **"Add Existing Files to Group 'System'..."**
2. 选择以下文件：
   - `stm32f4xx_it.c`
   - `system_stm32f4xx.c`
3. 点击 **"Add"**，然后 **"Close"**

## ⚙️ 第四步：配置项目设置

### 4.1 打开项目选项
1. 右键点击项目名称 "Snake_Game"
2. 选择 **"Options for Target 'Target 1'..."**
3. 或使用快捷键 **Alt+F7**

### 4.2 配置Target选项卡
在 "Target" 选项卡中：
1. **Xtal (MHz)**：设置为 `25.0` (外部晶振频率)
2. **Use On-chip ROM**：勾选
   - **Size**：`0x80000` (512KB Flash)
   - **Start**：`0x8000000`
3. **Use On-chip RAM**：勾选
   - **Size**：`0x20000` (128KB RAM)
   - **Start**：`0x20000000`

### 4.3 配置Output选项卡
在 "Output" 选项卡中：
1. **Name of Executable**：`Snake_Game`
2. **Select Folder for Objects**：设置为 `../Output/`
3. 勾选 **"Create HEX File"** ✅
4. 勾选 **"Browse Information"**

### 4.4 配置Listing选项卡
在 "Listing" 选项卡中：
1. **Select Folder for Listings**：设置为 `../Output/`

### 4.5 配置C/C++选项卡
在 "C/C++" 选项卡中：

**Include Paths**：点击 "..." 按钮，添加：
```
../Include
../Source
```

**Define**：添加以下宏定义：
```
USE_HAL_DRIVER
STM32F411xE
```

**Optimization**：选择 **"Level 1 (-O1)"**

### 4.6 配置Debug选项卡
在 "Debug" 选项卡中：
1. **Use**：选择 **"ST-Link Debugger"**
2. 点击 **"Settings"** 按钮
3. 在弹出窗口中：
   - **Port**：选择 **"SW"** (Serial Wire)
   - **Max Clock**：设置为 **"10 MHz"**
   - 点击 **"OK"**

### 4.7 配置Utilities选项卡
在 "Utilities" 选项卡中：
1. 勾选 **"Update Target before Debugging"**
2. **Use Target Driver for Flash Programming**：选择 **"ST-Link Debugger"**

点击 **"OK"** 保存所有设置。

## 🔧 第五步：修改代码适配Keil

### 5.1 修改stm32f4xx_hal_conf.h
由于Keil使用不同的HAL库结构，需要修改配置文件：

在 `Include/stm32f4xx_hal_conf.h` 文件中，确保包含路径正确：
```c
/* 在文件开头添加 */
#include "stm32f4xx.h"

/* 确保以下模块被启用 */
#define HAL_MODULE_ENABLED
#define HAL_GPIO_MODULE_ENABLED
#define HAL_RCC_MODULE_ENABLED
#define HAL_CORTEX_MODULE_ENABLED
#define HAL_PWR_MODULE_ENABLED
#define HAL_FLASH_MODULE_ENABLED
```

### 5.2 检查包含文件
确保所有 `.c` 文件中的包含语句正确：
```c
#include "stm32f4xx_hal.h"  // Keil HAL库主头文件
```

## 🔨 第六步：编译项目

### 6.1 首次编译
1. 点击工具栏上的 **"Build"** 按钮 (🔨)
2. 或使用快捷键 **F7**
3. 或菜单：**Project** → **Build Target**

### 6.2 查看编译结果
在底部的 "Build Output" 窗口中查看编译信息：

**成功编译的输出示例：**
```
Build started: Project: Snake_Game
*** Using Compiler 'V6.16', folder: 'C:\Keil_v5\ARM\ARMCLANG\Bin'
Build target 'Target 1'
compiling main.c...
compiling lcd_driver.c...
compiling snake_game.c...
compiling stm32f4xx_it.c...
compiling system_stm32f4xx.c...
linking...
Program Size: Code=15234 RO-data=1456 RW-data=128 ZI-data=2048
"Snake_Game.axf" - 0 Error(s), 0 Warning(s).
Build Time Elapsed:  00:00:03
```

### 6.3 检查生成的文件
编译成功后，在 `Output/` 文件夹中应该有：
- `Snake_Game.axf` (ELF格式可执行文件)
- `Snake_Game.hex` (Intel HEX格式，用于烧录)
- `Snake_Game.bin` (二进制文件)
- `Snake_Game.map` (内存映射文件)

## 🚨 常见编译错误及解决方法

### 错误1：找不到头文件
```
error: 'stm32f4xx_hal.h' file not found
```
**解决方法：**
1. 检查Include Paths设置
2. 确保HAL库组件已正确选择
3. 重新配置Run-Time Environment

### 错误2：未定义的符号
```
error: undefined symbol 'HAL_GPIO_Init'
```
**解决方法：**
1. 在Run-Time Environment中添加相应的HAL模块
2. 检查stm32f4xx_hal_conf.h中的模块启用设置

### 错误3：内存不足
```
error: region 'ROM' overflowed
```
**解决方法：**
1. 检查Target设置中的内存配置
2. 优化代码以减少内存使用
3. 确认使用的是免费版Keil（32KB限制）

## ✅ 编译成功检查清单

编译成功后，请确认：
- [ ] 编译输出显示 "0 Error(s), 0 Warning(s)"
- [ ] 生成了 `.hex` 文件
- [ ] 代码大小在32KB以内（免费版限制）
- [ ] 所有源文件都被正确编译

## 🎉 项目创建完成

恭喜！您已经成功在Keil uVision5中创建了STM32贪吃蛇项目。
生成的 `Snake_Game.hex` 文件可以直接用于烧录到STM32F411CEU6单片机。
