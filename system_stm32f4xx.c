/**
 * @file system_stm32f4xx.c
 * @brief CMSIS Cortex-M4设备外设访问层系统源文件
 * <AUTHOR> Assistant
 * @date 2024
 * 
 * 此文件提供两个函数和一个全局变量，用于：
 * - 系统时钟配置
 * - 系统时钟频率获取
 * - 系统时钟频率变量
 */

#include "stm32f4xx.h"

#if !defined(HSE_VALUE)
#define HSE_VALUE    25000000U /*!< 外部振荡器的值，单位为Hz */
#endif /* HSE_VALUE */

#if !defined(HSI_VALUE)
#define HSI_VALUE    16000000U /*!< 内部振荡器的值，单位为Hz */
#endif /* HSI_VALUE */

/************************* 杂项配置 ************************/
/*!< 取消注释以下行，如果您需要在Data Cache启用时重新定位向量表到内部SRAM */
/* #define VECT_TAB_SRAM */
#define VECT_TAB_OFFSET  0x00 /*!< 向量表基偏移字段。此值必须是0x200的倍数 */
/******************************************************************************/

/**
 * @brief 系统时钟频率 (核心时钟)
 * 
 * 此变量由SystemInit()函数更新，每当核心时钟配置改变时。
 * 用户应用程序可以使用此变量来获取核心时钟频率。
 * 在系统时钟配置改变后，用户应用程序有责任更新此变量。
 */
uint32_t SystemCoreClock = 16000000;
const uint8_t AHBPrescTable[16] = {0, 0, 0, 0, 0, 0, 0, 0, 1, 2, 3, 4, 6, 7, 8, 9};
const uint8_t APBPrescTable[8]  = {0, 0, 0, 0, 1, 2, 3, 4};

/**
 * @brief 设置微控制器系统
 * 初始化嵌入式Flash接口，PLL和更新SystemFrequency变量
 * @note 此函数应该在程序启动时使用，在主程序之前
 * @param 无
 * @retval 无
 */
void SystemInit(void)
{
    /* FPU设置 ------------------------------------------------------------*/
#if (__FPU_PRESENT == 1) && (__FPU_USED == 1)
    SCB->CPACR |= ((3UL << 10*2)|(3UL << 11*2));  /* 设置CP10和CP11完全访问 */
#endif

    /* 重置RCC时钟配置为默认重置状态 ------------*/
    /* 设置HSION位 */
    RCC->CR |= (uint32_t)0x00000001;

    /* 重置CFGR寄存器 */
    RCC->CFGR = 0x00000000;

    /* 重置HSEON, CSSON和PLLON位 */
    RCC->CR &= (uint32_t)0xFEF6FFFF;

    /* 重置PLLCFGR寄存器 */
    RCC->PLLCFGR = 0x24003010;

    /* 重置HSEBYP位 */
    RCC->CR &= (uint32_t)0xFFFBFFFF;

    /* 禁用所有中断 */
    RCC->CIR = 0x00000000;

#if defined(DATA_IN_ExtSRAM) || defined(DATA_IN_ExtSDRAM)
    SystemInit_ExtMemCtl(); 
#endif /* DATA_IN_ExtSRAM || DATA_IN_ExtSDRAM */

    /* 配置向量表位置 */
#ifdef VECT_TAB_SRAM
    SCB->VTOR = SRAM_BASE | VECT_TAB_OFFSET; /* 向量表重新定位在内部SRAM */
#else
    SCB->VTOR = FLASH_BASE | VECT_TAB_OFFSET; /* 向量表重新定位在内部FLASH */
#endif
}

/**
 * @brief 更新SystemCoreClock变量根据时钟寄存器值
 * SystemCoreClock变量包含核心时钟(HCLK)，它可以用作
 * 用户应用程序来设置SysTick定时器或配置其他参数
 * 
 * @note 每次核心时钟(HCLK)配置改变时，必须调用此函数
 *       来更新SystemCoreClock变量值。否则，任何基于此变量
 *       的配置都将是不正确的
 * 
 * @note - 系统频率计算基于HSI振荡器值
 *       - 此函数的结果可能不正确，当使用分数值为PLL_VCO时
 * 
 * @param 无
 * @retval 无
 */
void SystemCoreClockUpdate(void)
{
    uint32_t tmp = 0, pllvco = 0, pllp = 2, pllsource = 0, pllm = 2;
    
    /* 获取SYSCLK源 -------------------------------------------------------*/
    tmp = RCC->CFGR & RCC_CFGR_SWS;

    switch (tmp)
    {
        case 0x00:  /* HSI用作系统时钟源 */
            SystemCoreClock = HSI_VALUE;
            break;
        case 0x04:  /* HSE用作系统时钟源 */
            SystemCoreClock = HSE_VALUE;
            break;
        case 0x08:  /* PLL用作系统时钟源 */

            /* PLL_VCO = (HSE_VALUE或HSI_VALUE / PLLM) * PLLN
               SYSCLK = PLL_VCO / PLLP
            */    
            pllsource = (RCC->PLLCFGR & RCC_PLLCFGR_PLLSRC) >> 22;
            pllm = RCC->PLLCFGR & RCC_PLLCFGR_PLLM;
            
            if (pllsource != 0)
            {
                /* HSE用作PLL时钟源 */
                pllvco = (HSE_VALUE / pllm) * ((RCC->PLLCFGR & RCC_PLLCFGR_PLLN) >> 6);
            }
            else
            {
                /* HSI用作PLL时钟源 */
                pllvco = (HSI_VALUE / pllm) * ((RCC->PLLCFGR & RCC_PLLCFGR_PLLN) >> 6);      
            }

            pllp = (((RCC->PLLCFGR & RCC_PLLCFGR_PLLP) >>16) + 1 ) *2;
            SystemCoreClock = pllvco/pllp;
            break;
        default:
            SystemCoreClock = HSI_VALUE;
            break;
    }
    /* 计算HCLK频率 --------------------------------------------------*/
    /* 获取HCLK预分频器 */
    tmp = AHBPrescTable[((RCC->CFGR & RCC_CFGR_HPRE) >> 4)];
    /* HCLK频率 */
    SystemCoreClock >>= tmp;
}

#if defined(DATA_IN_ExtSRAM) || defined(DATA_IN_ExtSDRAM)
/**
 * @brief 设置外部内存控制器
 * 在跳转到主程序之前调用
 * 此函数配置外部SRAM/SDRAM挂载在FMC控制器
 * 并且必须在重新定位之前调用
 * @param 无
 * @retval 无
 */ 
void SystemInit_ExtMemCtl(void)
{
    /* 用户可以在这里添加自己的外部内存初始化代码 */
}
#endif /* DATA_IN_ExtSRAM || DATA_IN_ExtSDRAM */
