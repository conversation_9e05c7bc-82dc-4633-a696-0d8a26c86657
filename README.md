# STM32F411CEU6 贪吃蛇游戏项目

## 项目简介

这是一个基于STM32F411CEU6单片机和CL18CG958-18B LCD显示屏的贪吃蛇游戏项目。项目使用C语言编写，包含完整的LCD驱动程序和游戏逻辑实现。

## 硬件要求

### 主控芯片
- STM32F411CEU6 (Cortex-M4, 84MHz)

### 显示屏
- CL18CG958-18B (1.8寸 SPI接口 LCD显示屏)
- 驱动芯片: ST7735
- 分辨率: 128x160像素
- 颜色: RGB565 (16位色彩)

### 硬件连接

#### LCD显示屏连接
```
STM32F411CEU6    →    CL18CG958-18B
PB2              →    LCD_RESET (复位引脚)
PB3              →    LCD_SCK   (SPI时钟引脚)
PB4              →    LCD_DC    (数据/命令选择引脚)
PB5              →    LCD_SDA   (SPI数据引脚)
PA15             →    LCD_CS    (片选引脚)
3.3V             →    VCC       (电源正极)
GND              →    GND       (电源负极)
```

#### 按键连接
```
STM32F411CEU6    →    按键
PA0              →    KEY_UP    (上键)
PA1              →    KEY_DOWN  (下键)
PA2              →    KEY_LEFT  (左键)
PA3              →    KEY_RIGHT (右键)
```

注意：按键需要连接到GND，内部已配置上拉电阻。

## 软件架构

### 文件结构
```
├── main.c              # 主程序文件
├── main.h              # 主程序头文件
├── lcd_driver.c        # LCD驱动实现
├── lcd_driver.h        # LCD驱动头文件
├── snake_game.c        # 贪吃蛇游戏实现
├── snake_game.h        # 贪吃蛇游戏头文件
├── stm32f4xx_it.c      # 中断服务程序
├── stm32f4xx_it.h      # 中断服务程序头文件
├── stm32f4xx_hal_conf.h # HAL库配置文件
├── system_stm32f4xx.c  # 系统初始化文件
├── Makefile            # 编译配置文件
└── README.md           # 项目说明文件
```

### 功能模块

#### 1. LCD驱动模块 (lcd_driver.c/h)
- 软件SPI通信实现
- ST7735驱动芯片初始化
- 基本图形绘制功能 (像素、直线、矩形、圆形)
- 文字显示功能 (字符、字符串、数字)
- 颜色管理 (RGB565格式)

#### 2. 贪吃蛇游戏模块 (snake_game.c/h)
- 游戏逻辑实现
- 蛇的移动和碰撞检测
- 食物生成和消费
- 得分和等级系统
- 游戏状态管理
- 用户输入处理

#### 3. 系统管理模块
- 时钟配置 (84MHz)
- GPIO初始化
- 中断处理
- 系统滴答定时器

## 游戏特性

### 游戏规则
1. 使用方向键控制蛇的移动方向
2. 蛇吃到食物后会增长并获得分数
3. 蛇撞到自己身体时游戏结束
4. 蛇可以穿墙移动 (从一边出现在另一边)
5. 随着得分增加，游戏速度会逐渐提升

### 游戏参数
- 游戏区域: 112x120像素 (28x30网格)
- 蛇身方块大小: 4x4像素
- 初始蛇长: 3节
- 最大蛇长: 200节
- 初始速度: 500ms/移动
- 每级速度增加: 20ms
- 最高速度: 100ms/移动
- 每个食物得分: 10分
- 升级所需得分: 100分

### 显示界面
- 游戏区域: 带边框的游戏场地
- 蛇头: 绿色方块
- 蛇身: 深灰色方块
- 食物: 红色方块
- 得分显示: 屏幕上方
- 等级显示: 屏幕下方
- 游戏结束提示: 居中显示

## 开发环境配置

### 必需工具
1. **ARM GCC工具链**
   - arm-none-eabi-gcc
   - arm-none-eabi-objcopy
   - arm-none-eabi-size

2. **构建工具**
   - GNU Make

3. **调试工具** (可选)
   - OpenOCD
   - ST-Link
   - GDB

### 编译步骤

1. **安装ARM GCC工具链**
   ```bash
   # Ubuntu/Debian
   sudo apt-get install gcc-arm-none-eabi
   
   # Windows
   # 下载并安装 GNU Arm Embedded Toolchain
   ```

2. **克隆或下载项目代码**
   ```bash
   git clone <项目地址>
   cd stm32f411-snake-game
   ```

3. **编译项目**
   ```bash
   make clean
   make all
   ```

4. **编译输出**
   - `build/snake_game.elf` - ELF可执行文件
   - `build/snake_game.hex` - Intel HEX格式文件
   - `build/snake_game.bin` - 二进制文件

### 烧录程序

#### 使用ST-Link
```bash
# 使用OpenOCD烧录
openocd -f interface/stlink.cfg -f target/stm32f4x.cfg -c "program build/snake_game.elf verify reset exit"

# 或使用st-flash
st-flash write build/snake_game.bin 0x8000000
```

#### 使用STM32CubeProgrammer
1. 打开STM32CubeProgrammer
2. 连接ST-Link
3. 选择 `build/snake_game.hex` 文件
4. 点击"Download"烧录程序

## 使用说明

### 游戏操作
1. **开始游戏**: 上电后自动开始
2. **控制蛇移动**: 
   - PA0 (上键): 向上移动
   - PA1 (下键): 向下移动  
   - PA2 (左键): 向左移动
   - PA3 (右键): 向右移动
3. **游戏结束**: 按任意键重新开始

### 注意事项
1. 确保硬件连接正确
2. 检查电源供应稳定 (3.3V)
3. 按键需要连接到GND
4. LCD显示屏需要正确的SPI时序

## 故障排除

### 常见问题

1. **LCD无显示**
   - 检查SPI连接线
   - 确认电源供应
   - 检查复位信号

2. **按键无响应**
   - 检查按键连接
   - 确认GPIO配置
   - 测试按键电路

3. **程序无法运行**
   - 检查时钟配置
   - 确认程序烧录成功
   - 检查复位电路

4. **游戏运行异常**
   - 检查内存使用
   - 确认中断配置
   - 验证游戏逻辑

### 调试方法
1. 使用串口输出调试信息
2. 使用LED指示程序状态
3. 使用调试器单步执行
4. 检查寄存器状态

## 扩展功能

### 可能的改进
1. 添加音效支持
2. 实现多种游戏模式
3. 添加最高分记录
4. 实现游戏暂停功能
5. 添加更多视觉效果
6. 支持更大的游戏区域
7. 实现网络对战功能

### 硬件扩展
1. 添加蜂鸣器
2. 增加更多按键
3. 使用更大的显示屏
4. 添加SD卡存储
5. 集成无线通信模块

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交问题报告和改进建议！

## 联系方式

如有问题，请通过以下方式联系：
- 邮箱: [您的邮箱]
- GitHub: [您的GitHub]

---

**祝您游戏愉快！** 🐍🎮
