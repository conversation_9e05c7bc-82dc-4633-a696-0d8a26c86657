# STM32F411CEU6贪吃蛇游戏 - Keil uVision5初学者完整操作指南

## 🎯 操作前准备

### 确认文件准备就绪
**项目位置**: `C:\Users\<USER>\Desktop\STM32_Snake_Game`

**必需文件检查**:
```
STM32_Snake_Game/
├── Source/         (6个文件)
│   ├── main.c
│   ├── lcd_driver.c
│   ├── snake_game.c
│   ├── stm32f4xx_it.c
│   ├── system_stm32f4xx.c
│   └── startup_stm32f411xe.s
└── Include/        (5个文件)
    ├── main.h
    ├── lcd_driver.h
    ├── snake_game.h
    ├── stm32f4xx_it.h
    └── stm32f4xx_hal_conf.h
```

## 🚀 第一部分：启动Keil并创建项目

### 步骤1：启动Keil uVision5
1. **双击桌面图标**: 找到并双击 "Keil uVision5" 图标
2. **等待加载**: 软件启动需要10-15秒，请耐心等待
3. **确认启动**: 看到Keil主界面后继续下一步

### 步骤2：创建新项目
1. **点击菜单**: 在顶部菜单栏点击 **"Project"**
2. **选择新建**: 在下拉菜单中点击 **"New uVision Project..."**
3. **等待对话框**: 会弹出 "Create New Project" 对话框

### 步骤3：设置项目保存位置
1. **浏览文件夹**: 在对话框中点击左侧的文件夹图标浏览
2. **导航到项目目录**: 
   - 点击 "桌面"
   - 双击 "STM32_Snake_Game" 文件夹
   - 双击 "Project" 文件夹
3. **输入项目名称**: 在 "文件名" 框中输入: `Snake_Game`
4. **保存项目**: 点击 **"保存"** 按钮

### 步骤4：选择目标芯片
会弹出 "Select Device for Target 'Target 1'" 对话框:

1. **展开厂商**: 在左侧列表中找到并点击 **"STMicroelectronics"** 前的 **"+"** 号
2. **展开系列**: 找到并点击 **"STM32F4 Series"** 前的 **"+"** 号
3. **展开型号**: 找到并点击 **"STM32F411"** 前的 **"+"** 号
4. **选择芯片**: 在右侧列表中点击选择 **"STM32F411CEUx"**
5. **确认选择**: 点击 **"OK"** 按钮

### 步骤5：配置运行时环境
会弹出 "Manage Run-Time Environment" 对话框:

1. **展开CMSIS**: 点击 **"CMSIS"** 前的 **"+"** 号
2. **选择CORE**: 勾选 **"CORE"** 复选框 ✅
3. **展开Device**: 点击 **"Device"** 前的 **"+"** 号
4. **选择Startup**: 勾选 **"Startup"** 复选框 ✅
5. **检查依赖**: 确认右下角显示 "No unresolved dependencies"
6. **确认配置**: 点击 **"OK"** 按钮

## 📁 第二部分：添加源文件到项目

### 步骤6：创建文件组
在左侧的项目窗口中:

1. **右键点击Target 1**: 在 "Target 1" 上点击鼠标右键
2. **添加组**: 选择 **"Add Group..."**
3. **输入组名**: 输入 `Application`，点击 **"OK"**
4. **重复创建**: 用同样方法创建以下组:
   - `Drivers`
   - `System`

### 步骤7：添加Application组文件
1. **右键Application组**: 在 "Application" 组上点击鼠标右键
2. **选择添加文件**: 点击 **"Add Existing Files to Group 'Application'..."**
3. **浏览到Source目录**: 
   - 点击向上箭头 "↑" 返回上级目录
   - 双击 "Source" 文件夹
4. **选择文件**: 按住 **Ctrl** 键，依次点击选择:
   - `main.c`
   - `snake_game.c`
5. **添加文件**: 点击 **"Add"** 按钮
6. **关闭对话框**: 点击 **"Close"** 按钮

### 步骤8：添加Drivers组文件
1. **右键Drivers组**: 在 "Drivers" 组上点击鼠标右键
2. **选择添加文件**: 点击 **"Add Existing Files to Group 'Drivers'..."**
3. **确认在Source目录**: 应该已经在Source目录中
4. **选择文件**: 点击选择 `lcd_driver.c`
5. **添加文件**: 点击 **"Add"** 按钮
6. **关闭对话框**: 点击 **"Close"** 按钮

### 步骤9：添加System组文件
1. **右键System组**: 在 "System" 组上点击鼠标右键
2. **选择添加文件**: 点击 **"Add Existing Files to Group 'System'..."**
3. **确认在Source目录**: 应该已经在Source目录中
4. **选择文件**: 按住 **Ctrl** 键，依次点击选择:
   - `stm32f4xx_it.c`
   - `system_stm32f4xx.c`
5. **添加文件**: 点击 **"Add"** 按钮
6. **关闭对话框**: 点击 **"Close"** 按钮

## ⚙️ 第三部分：配置项目选项

### 步骤10：打开项目选项
1. **右键项目名**: 在项目名 "Snake_Game" 上点击鼠标右键
2. **选择选项**: 点击 **"Options for Target 'Target 1'..."**
3. **或使用快捷键**: 按 **Alt + F7** 键

### 步骤11：配置Target选项卡
在弹出的对话框中，确认当前在 **"Target"** 选项卡:

1. **设置时钟频率**: 
   - 找到 "Xtal (MHz)" 输入框
   - 输入: `16.0`

2. **配置Flash内存**:
   - 确认 "IROM1" 已勾选 ✅
   - Start: `0x8000000`
   - Size: `0x80000`

3. **配置RAM内存**:
   - 确认 "IRAM1" 已勾选 ✅
   - Start: `0x20000000`
   - Size: `0x20000`

### 步骤12：配置Output选项卡
1. **点击Output选项卡**: 点击 **"Output"** 标签
2. **设置输出目录**: 
   - 找到 "Select Folder for Objects"
   - 点击右侧的 **"..."** 按钮
   - 浏览到项目的 "Output" 文件夹
   - 点击 **"确定"**
3. **启用HEX文件生成**: 
   - 找到 "Create HEX File" 复选框
   - **重要**: 勾选此选项 ✅
4. **其他选项**: 勾选 "Browse Information" ✅

### 步骤13：配置C/C++选项卡
1. **点击C/C++选项卡**: 点击 **"C/C++"** 标签
2. **设置包含路径**:
   - 找到 "Include Paths" 部分
   - 点击右侧的 **"..."** 按钮
   - 点击 **"New (Insert)"** 按钮 (文件夹图标)
   - 浏览并添加以下路径:
     - `../Include`
     - `../Source`
   - 点击 **"OK"**

3. **设置宏定义**:
   - 找到 "Define" 输入框
   - 输入: `USE_HAL_DRIVER,STM32F411xE`

4. **设置优化级别**:
   - 找到 "Optimization" 下拉菜单
   - 选择: **"Level 1 (-O1)"**

### 步骤14：保存配置
点击 **"OK"** 按钮保存所有配置

## 🔨 第四部分：编译项目

### 步骤15：开始编译
1. **点击编译按钮**: 在工具栏找到并点击 **"Build"** 按钮 (🔨 图标)
2. **或使用快捷键**: 按 **F7** 键
3. **或使用菜单**: 点击 **"Project"** → **"Build Target"**

### 步骤16：观察编译过程
在底部的 **"Build Output"** 窗口中观察编译进度:

**正常编译过程显示**:
```
Build started: Project: Snake_Game
*** Using Compiler 'V6.16'
Build target 'Target 1'
compiling main.c...
compiling snake_game.c...
compiling lcd_driver.c...
compiling stm32f4xx_it.c...
compiling system_stm32f4xx.c...
assembling startup_stm32f411xe.s...
linking...
Program Size: Code=15234 RO-data=1456 RW-data=128 ZI-data=2048
"Snake_Game.axf" - 0 Error(s), 0 Warning(s).
Build Time Elapsed:  00:00:05
```

### 步骤17：判断编译结果
**编译成功标志**:
- 看到 **"0 Error(s), 0 Warning(s)"**
- 显示 **"Program Size"** 信息
- 显示 **"Build Time Elapsed"**

**如果编译失败**:
- 会显示错误数量，如 "1 Error(s)"
- 在输出窗口中会显示具体错误信息
- 需要根据错误信息进行修正

## ✅ 第五部分：验证编译结果

### 步骤18：检查生成的文件
1. **打开文件管理器**: 按 **Win + E** 键
2. **导航到输出目录**: 
   - 进入桌面
   - 打开 "STM32_Snake_Game" 文件夹
   - 打开 "Output" 文件夹
3. **确认文件存在**:
   - **Snake_Game.hex** ← 这是您需要的烧录文件！
   - Snake_Game.axf
   - Snake_Game.map

### 步骤19：验证HEX文件
1. **检查文件大小**: 
   - 右键点击 "Snake_Game.hex"
   - 选择 "属性"
   - 文件大小应该在 30-80KB 范围内

2. **检查文件内容**:
   - 用记事本打开 "Snake_Game.hex"
   - 应该看到以 `:` 开头的十六进制行
   - 第一行类似: `:020000040800F2`
   - 最后一行应该是: `:00000001FF`

## 🎉 完成！

如果您看到了 **"0 Error(s), 0 Warning(s)"** 并且在Output文件夹中找到了 **Snake_Game.hex** 文件，恭喜您！

**您已经成功编译了STM32F411CEU6贪吃蛇游戏项目！**

**生成的HEX文件位置**: 
`C:\Users\<USER>\Desktop\STM32_Snake_Game\Output\Snake_Game.hex`

这个文件就可以直接烧录到STM32F411CEU6单片机了！

## 🚨 常见问题解决

### 如果编译出现错误
1. **找不到头文件**: 检查Include路径设置是否正确
2. **未定义的符号**: 检查宏定义是否正确设置
3. **链接错误**: 确认所有源文件都已添加到项目中

### 如果找不到HEX文件
1. 确认在Output选项卡中勾选了 "Create HEX File"
2. 重新编译项目
3. 检查编译是否真的成功（0 Error(s)）
