@echo off
chcp 65001 >nul
echo ========================================
echo STM32贪吃蛇游戏 - Keil项目自动设置
echo ========================================
echo.

:: 设置目标项目路径
set TARGET_DIR=C:\Users\<USER>\Desktop\STM32_Snake_Keil

:: 检查目标目录是否存在
if not exist "%TARGET_DIR%" (
    echo [错误] 目标目录不存在: %TARGET_DIR%
    echo 请先创建该目录
    pause
    exit /b 1
)

echo 目标项目路径: %TARGET_DIR%
echo.

:: 创建项目子目录
echo 1. 创建项目目录结构...
if not exist "%TARGET_DIR%\Project" mkdir "%TARGET_DIR%\Project"
if not exist "%TARGET_DIR%\Source" mkdir "%TARGET_DIR%\Source"
if not exist "%TARGET_DIR%\Include" mkdir "%TARGET_DIR%\Include"
if not exist "%TARGET_DIR%\Output" mkdir "%TARGET_DIR%\Output"
echo [成功] 目录结构创建完成

:: 检查当前目录中的源文件
echo.
echo 2. 检查源文件...
set missing_files=0

if not exist "main.c" (
    echo [错误] 缺少文件: main.c
    set missing_files=1
)
if not exist "lcd_driver.c" (
    echo [错误] 缺少文件: lcd_driver.c
    set missing_files=1
)
if not exist "snake_game.c" (
    echo [错误] 缺少文件: snake_game.c
    set missing_files=1
)
if not exist "stm32f4xx_it.c" (
    echo [错误] 缺少文件: stm32f4xx_it.c
    set missing_files=1
)
if not exist "system_stm32f4xx.c" (
    echo [错误] 缺少文件: system_stm32f4xx.c
    set missing_files=1
)
if not exist "startup_stm32f411xe.s" (
    echo [错误] 缺少文件: startup_stm32f411xe.s
    set missing_files=1
)

if %missing_files% equ 1 (
    echo.
    echo 请确保所有源文件都在当前目录中
    pause
    exit /b 1
)
echo [成功] 所有源文件检查完毕

:: 复制源代码文件
echo.
echo 3. 复制源代码文件到 %TARGET_DIR%\Source\...
copy "main.c" "%TARGET_DIR%\Source\" >nul
if %errorlevel% neq 0 echo [错误] 复制main.c失败 && goto :error
echo   ✅ main.c

copy "lcd_driver.c" "%TARGET_DIR%\Source\" >nul
if %errorlevel% neq 0 echo [错误] 复制lcd_driver.c失败 && goto :error
echo   ✅ lcd_driver.c

copy "snake_game.c" "%TARGET_DIR%\Source\" >nul
if %errorlevel% neq 0 echo [错误] 复制snake_game.c失败 && goto :error
echo   ✅ snake_game.c

copy "stm32f4xx_it.c" "%TARGET_DIR%\Source\" >nul
if %errorlevel% neq 0 echo [错误] 复制stm32f4xx_it.c失败 && goto :error
echo   ✅ stm32f4xx_it.c

copy "system_stm32f4xx.c" "%TARGET_DIR%\Source\" >nul
if %errorlevel% neq 0 echo [错误] 复制system_stm32f4xx.c失败 && goto :error
echo   ✅ system_stm32f4xx.c

copy "startup_stm32f411xe.s" "%TARGET_DIR%\Source\" >nul
if %errorlevel% neq 0 echo [错误] 复制startup_stm32f411xe.s失败 && goto :error
echo   ✅ startup_stm32f411xe.s

echo [成功] 源代码文件复制完成

:: 复制头文件
echo.
echo 4. 复制头文件到 %TARGET_DIR%\Include\...
copy "main.h" "%TARGET_DIR%\Include\" >nul
if %errorlevel% neq 0 echo [错误] 复制main.h失败 && goto :error
echo   ✅ main.h

copy "lcd_driver.h" "%TARGET_DIR%\Include\" >nul
if %errorlevel% neq 0 echo [错误] 复制lcd_driver.h失败 && goto :error
echo   ✅ lcd_driver.h

copy "snake_game.h" "%TARGET_DIR%\Include\" >nul
if %errorlevel% neq 0 echo [错误] 复制snake_game.h失败 && goto :error
echo   ✅ snake_game.h

copy "stm32f4xx_it.h" "%TARGET_DIR%\Include\" >nul
if %errorlevel% neq 0 echo [错误] 复制stm32f4xx_it.h失败 && goto :error
echo   ✅ stm32f4xx_it.h

copy "stm32f4xx_hal_conf.h" "%TARGET_DIR%\Include\" >nul
if %errorlevel% neq 0 echo [错误] 复制stm32f4xx_hal_conf.h失败 && goto :error
echo   ✅ stm32f4xx_hal_conf.h

echo [成功] 头文件复制完成

:: 验证文件复制结果
echo.
echo 5. 验证文件复制结果...
echo.
echo Source目录文件:
dir "%TARGET_DIR%\Source" /b
echo.
echo Include目录文件:
dir "%TARGET_DIR%\Include" /b

:: 创建项目说明文件
echo.
echo 6. 创建项目说明文件...
(
echo # STM32F411CEU6贪吃蛇游戏项目
echo.
echo ## 项目结构
echo ```
echo STM32_Snake_Keil/
echo ├── Project/        ^(Keil项目文件^)
echo ├── Source/         ^(源代码文件 - 6个文件^)
echo ├── Include/        ^(头文件 - 5个文件^)
echo └── Output/         ^(编译输出^)
echo ```
echo.
echo ## 下一步操作
echo 1. 启动Keil uVision5
echo 2. 创建新项目: Project → New uVision Project
echo 3. 保存位置: Project文件夹
echo 4. 项目名称: Snake_Game
echo 5. 选择芯片: STM32F411CEUx
echo 6. 配置运行时环境: CMSIS-CORE, Device-Startup
echo 7. 添加源文件到项目
echo 8. 配置项目选项
echo 9. 编译项目
echo.
echo ## 硬件连接
echo ### LCD显示屏连接
echo ```
echo STM32F411CEU6    →    CL18CG958-18B
echo PB2              →    RESET
echo PB3              →    SCK
echo PB4              →    DC
echo PB5              →    SDA
echo PA15             →    CS
echo 3.3V             →    VCC
echo GND              →    GND
echo ```
echo.
echo ### 按键连接
echo ```
echo STM32F411CEU6    →    按键
echo PA0              →    上键 ^(连接到GND^)
echo PA1              →    下键 ^(连接到GND^)
echo PA2              →    左键 ^(连接到GND^)
echo PA3              →    右键 ^(连接到GND^)
echo ```
echo.
echo 项目文件准备完成！🐍🎮
) > "%TARGET_DIR%\README.md"

echo [成功] 项目说明文件创建完成

:: 显示完成信息
echo.
echo ========================================
echo 🎉 Keil项目设置完成！
echo ========================================
echo.
echo 📁 项目位置: %TARGET_DIR%
echo.
echo 📋 文件统计:
echo   ✅ Source/     - 6个源代码文件
echo   ✅ Include/    - 5个头文件
echo   ✅ Project/    - Keil项目目录 ^(准备就绪^)
echo   ✅ Output/     - 编译输出目录 ^(准备就绪^)
echo   ✅ README.md   - 项目说明文件
echo.
echo 🔧 下一步:
echo   1. 启动Keil uVision5
echo   2. 按照详细步骤创建项目
echo   3. 添加源文件并编译
echo.

:: 询问是否打开项目文件夹
echo 是否现在打开项目文件夹？ (Y/N)
set /p open_folder=
if /i "%open_folder%"=="Y" (
    explorer "%TARGET_DIR%"
)

goto :end

:error
echo.
echo [错误] 文件复制过程中出现错误
echo 请检查文件权限和磁盘空间
pause
exit /b 1

:end
echo.
echo 按任意键退出...
pause >nul
