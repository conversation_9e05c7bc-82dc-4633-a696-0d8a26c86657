@echo off
chcp 65001 >nul
echo ========================================
echo STM32F411CEU6贪吃蛇游戏完整项目创建
echo ========================================
echo.

:: 设置项目目录
set PROJECT_DIR=%USERPROFILE%\Desktop\STM32_Snake_Game

echo 项目将创建在: %PROJECT_DIR%
echo.

:: 创建项目目录结构
echo 1. 创建项目目录结构...
if exist "%PROJECT_DIR%" (
    echo 目录已存在，是否删除重新创建？ (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        rmdir /s /q "%PROJECT_DIR%"
        echo 已删除旧目录
    ) else (
        echo 保留现有目录
    )
)

mkdir "%PROJECT_DIR%" 2>nul
mkdir "%PROJECT_DIR%\Project" 2>nul
mkdir "%PROJECT_DIR%\Source" 2>nul
mkdir "%PROJECT_DIR%\Include" 2>nul
mkdir "%PROJECT_DIR%\Output" 2>nul
mkdir "%PROJECT_DIR%\Docs" 2>nul

echo [成功] 目录结构创建完成
echo.

:: 检查当前目录中的源文件并复制
echo 2. 检查并复制源代码文件...

set files_copied=0

:: 复制源代码文件
if exist "main.c" (
    copy "main.c" "%PROJECT_DIR%\Source\" >nul
    echo   ✅ main.c
    set /a files_copied+=1
) else (
    echo   ❌ main.c ^(缺失^)
)

if exist "lcd_driver.c" (
    copy "lcd_driver.c" "%PROJECT_DIR%\Source\" >nul
    echo   ✅ lcd_driver.c
    set /a files_copied+=1
) else (
    echo   ❌ lcd_driver.c ^(缺失^)
)

if exist "snake_game.c" (
    copy "snake_game.c" "%PROJECT_DIR%\Source\" >nul
    echo   ✅ snake_game.c
    set /a files_copied+=1
) else (
    echo   ❌ snake_game.c ^(缺失^)
)

if exist "stm32f4xx_it.c" (
    copy "stm32f4xx_it.c" "%PROJECT_DIR%\Source\" >nul
    echo   ✅ stm32f4xx_it.c
    set /a files_copied+=1
) else (
    echo   ❌ stm32f4xx_it.c ^(缺失^)
)

if exist "system_stm32f4xx.c" (
    copy "system_stm32f4xx.c" "%PROJECT_DIR%\Source\" >nul
    echo   ✅ system_stm32f4xx.c
    set /a files_copied+=1
) else (
    echo   ❌ system_stm32f4xx.c ^(缺失^)
)

if exist "startup_stm32f411xe.s" (
    copy "startup_stm32f411xe.s" "%PROJECT_DIR%\Source\" >nul
    echo   ✅ startup_stm32f411xe.s
    set /a files_copied+=1
) else (
    echo   ❌ startup_stm32f411xe.s ^(缺失^)
)

echo.
echo 复制头文件:

:: 复制头文件
if exist "main.h" (
    copy "main.h" "%PROJECT_DIR%\Include\" >nul
    echo   ✅ main.h
    set /a files_copied+=1
) else (
    echo   ❌ main.h ^(缺失^)
)

if exist "lcd_driver.h" (
    copy "lcd_driver.h" "%PROJECT_DIR%\Include\" >nul
    echo   ✅ lcd_driver.h
    set /a files_copied+=1
) else (
    echo   ❌ lcd_driver.h ^(缺失^)
)

if exist "snake_game.h" (
    copy "snake_game.h" "%PROJECT_DIR%\Include\" >nul
    echo   ✅ snake_game.h
    set /a files_copied+=1
) else (
    echo   ❌ snake_game.h ^(缺失^)
)

if exist "stm32f4xx_it.h" (
    copy "stm32f4xx_it.h" "%PROJECT_DIR%\Include\" >nul
    echo   ✅ stm32f4xx_it.h
    set /a files_copied+=1
) else (
    echo   ❌ stm32f4xx_it.h ^(缺失^)
)

if exist "stm32f4xx_hal_conf.h" (
    copy "stm32f4xx_hal_conf.h" "%PROJECT_DIR%\Include\" >nul
    echo   ✅ stm32f4xx_hal_conf.h
    set /a files_copied+=1
) else (
    echo   ❌ stm32f4xx_hal_conf.h ^(缺失^)
)

echo.
echo 文件复制统计: %files_copied%/11 个文件

if %files_copied% LSS 11 (
    echo.
    echo ⚠️  警告: 部分源代码文件缺失
    echo 请确保以下文件都在当前目录中:
    echo.
    echo 源代码文件:
    echo   - main.c
    echo   - lcd_driver.c  
    echo   - snake_game.c
    echo   - stm32f4xx_it.c
    echo   - system_stm32f4xx.c
    echo   - startup_stm32f411xe.s
    echo.
    echo 头文件:
    echo   - main.h
    echo   - lcd_driver.h
    echo   - snake_game.h
    echo   - stm32f4xx_it.h
    echo   - stm32f4xx_hal_conf.h
    echo.
    echo 💡 解决方案:
    echo   1. 确认您在正确的目录中运行此脚本
    echo   2. 或者使用手动创建文件的方法
    echo   3. 查看项目说明文档获取完整的文件内容
    echo.
)

:: 创建项目说明文件
echo 3. 创建项目说明和操作指南...

:: 创建README文件
(
echo # STM32F411CEU6贪吃蛇游戏项目
echo.
echo ## 项目状态
echo 文件复制完成: %files_copied%/11 个文件
echo.
echo ## 项目文件结构
echo ```
echo STM32_Snake_Game/
echo ├── Project/        ^(Keil项目文件^)
echo ├── Source/         ^(源代码文件^)
echo ├── Include/        ^(头文件^)
echo ├── Output/         ^(编译输出^)
echo └── Docs/           ^(文档^)
echo ```
echo.
echo ## 下一步操作
echo 1. 检查所有源文件是否完整
echo 2. 阅读操作指南文档
echo 3. 启动Keil uVision5
echo 4. 按照指南创建项目
echo.
echo ## 硬件连接
echo ### LCD显示屏 ^(CL18CG958-18B^)
echo ```
echo STM32F411CEU6    →    LCD
echo PB2              →    RESET
echo PB3              →    SCK  
echo PB4              →    DC
echo PB5              →    SDA
echo PA15             →    CS
echo 3.3V             →    VCC
echo GND              →    GND
echo ```
echo.
echo ### 按键连接
echo ```
echo STM32F411CEU6    →    按键
echo PA0              →    上键 ^(连接到GND^)
echo PA1              →    下键 ^(连接到GND^)
echo PA2              →    左键 ^(连接到GND^)
echo PA3              →    右键 ^(连接到GND^)
echo ```
echo.
echo 项目准备完成！🐍🎮
) > "%PROJECT_DIR%\README.md"

:: 复制操作指南文档
if exist "Keil初学者完整操作指南.md" (
    copy "Keil初学者完整操作指南.md" "%PROJECT_DIR%\Docs\" >nul
    echo   ✅ Keil操作指南
)

if exist "Keil源代码修改指南.md" (
    copy "Keil源代码修改指南.md" "%PROJECT_DIR%\Docs\" >nul
    echo   ✅ 源代码修改指南
)

echo [成功] 项目文件创建完成
echo.

:: 显示完成信息
echo ========================================
echo 🎉 项目创建完成！
echo ========================================
echo.
echo 📁 项目位置: %PROJECT_DIR%
echo.
echo 📋 项目内容:
echo   ✅ Project/    - Keil项目目录
echo   ✅ Source/     - 源代码文件 ^(%files_copied%个^)
echo   ✅ Include/    - 头文件
echo   ✅ Output/     - 编译输出目录
echo   ✅ Docs/       - 操作指南文档
echo   ✅ README.md   - 项目说明
echo.

if %files_copied% EQU 11 (
    echo 🎉 所有文件复制成功！
    echo.
    echo 🔧 下一步操作:
    echo   1. 打开项目文件夹
    echo   2. 阅读 Docs 目录中的操作指南
    echo   3. 启动Keil uVision5
    echo   4. 按照指南创建项目并编译
) else (
    echo ⚠️  部分文件缺失，需要手动处理
    echo.
    echo 🔧 下一步操作:
    echo   1. 打开项目文件夹
    echo   2. 查看缺失的文件列表
    echo   3. 手动创建缺失的文件
    echo   4. 参考操作指南完成项目
)

echo.

:: 询问是否打开项目文件夹
echo 是否现在打开项目文件夹？ (Y/N)
set /p open_folder=
if /i "%open_folder%"=="Y" (
    explorer "%PROJECT_DIR%"
)

echo.
echo 按任意键退出...
pause >nul
