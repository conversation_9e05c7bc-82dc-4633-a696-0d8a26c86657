# Keil uVision5 MDK 安装指南

## 🎯 安装前准备

### 系统要求
- Windows 7/8/10/11 (32位或64位)
- 至少4GB RAM (推荐8GB)
- 至少10GB可用磁盘空间
- 管理员权限

### 下载地址
官方下载：https://www.keil.com/download/product/
选择：**MDK-Arm** (Microcontroller Development Kit)

## 📥 第一步：下载Keil MDK

### 1.1 访问官方网站
1. 打开浏览器，访问：https://www.keil.com/download/product/
2. 找到 "MDK-Arm" 部分
3. 点击 "Download MDK-Arm" 按钮

### 1.2 填写注册信息
1. 填写个人信息（姓名、邮箱、公司等）
2. 选择 "Evaluation" (评估版)
3. 点击 "Submit" 提交

### 1.3 下载安装包
1. 下载 `MDK539.EXE` (约800MB-1GB)
2. 等待下载完成

## 🔧 第二步：安装Keil MDK

### 2.1 运行安装程序
1. 右键点击 `MDK539.EXE`
2. 选择 "以管理员身份运行"
3. 如果出现安全警告，点击 "运行"

### 2.2 安装向导步骤
1. **欢迎界面**：点击 "Next"
2. **许可协议**：选择 "I agree"，点击 "Next"
3. **安装路径**：
   - 默认：`C:\Keil_v5\`
   - 建议保持默认，点击 "Next"
4. **客户信息**：
   - 填写姓名和公司信息
   - 邮箱地址（用于接收许可证）
   - 点击 "Next"
5. **开始安装**：点击 "Next" 开始安装

### 2.3 安装过程
1. 等待文件复制完成（约5-10分钟）
2. 安装完成后，会自动启动 "Pack Installer"

## 📦 第三步：安装STM32支持包

### 3.1 Pack Installer界面
安装完成后会自动打开Pack Installer，如果没有打开：
1. 开始菜单 → Keil uVision5 → Pack Installer
2. 或在uVision中：Project → Manage → Pack Installer

### 3.2 安装STM32F4支持包
1. 在左侧设备列表中找到 "STMicroelectronics"
2. 展开 "STM32F4 Series"
3. 找到 "STM32F411" 系列
4. 在右侧找到 "Keil::STM32F4xx_DFP"
5. 点击 "Install" 安装

### 3.3 等待安装完成
1. 下载和安装过程需要几分钟
2. 安装完成后，状态显示为 "Up to date"

## 🔑 第四步：许可证配置

### 4.1 启动uVision5
1. 双击桌面上的 "Keil uVision5" 图标
2. 或从开始菜单启动

### 4.2 许可证管理
1. 菜单：File → License Management
2. 会显示当前许可证状态

### 4.3 免费版限制
- **代码大小限制**：32KB
- **调试限制**：有限的调试功能
- **商业使用**：不允许商业使用

**注意**：我们的贪吃蛇项目代码大小约15-20KB，在免费版限制范围内。

## ✅ 第五步：验证安装

### 5.1 创建测试项目
1. 启动Keil uVision5
2. Project → New uVision Project
3. 选择保存位置，输入项目名称 "test_project"
4. 点击 "保存"

### 5.2 选择设备
1. 在设备选择对话框中：
2. 展开 "STMicroelectronics"
3. 展开 "STM32F4 Series"
4. 展开 "STM32F411"
5. 选择 "STM32F411CEUx"
6. 点击 "OK"

### 5.3 配置运行时环境
1. 在 "Manage Run-Time Environment" 对话框中：
2. 展开 "CMSIS"，勾选 "CORE"
3. 展开 "Device"，勾选 "Startup"
4. 点击 "OK"

### 5.4 验证项目创建
如果能成功创建项目并看到项目树，说明安装成功！

## 🚨 常见安装问题

### 问题1：安装失败
**错误**：安装程序无法运行
**解决**：
1. 确保以管理员身份运行
2. 关闭杀毒软件
3. 检查磁盘空间是否足够

### 问题2：Pack安装失败
**错误**：无法下载STM32支持包
**解决**：
1. 检查网络连接
2. 尝试手动下载Pack文件
3. 使用VPN或更换网络

### 问题3：许可证问题
**错误**：许可证无效
**解决**：
1. 确保填写了正确的邮箱地址
2. 检查垃圾邮件文件夹
3. 重新申请评估许可证

### 问题4：设备不显示
**错误**：找不到STM32F411设备
**解决**：
1. 确保安装了STM32F4xx_DFP包
2. 重启Keil uVision5
3. 重新安装设备支持包

## 📋 安装检查清单

安装完成后，请确认以下项目：
- [ ] Keil uVision5 能正常启动
- [ ] 可以创建新项目
- [ ] 能找到STM32F411CEUx设备
- [ ] Pack Installer显示STM32F4xx_DFP已安装
- [ ] 许可证状态正常（评估版）

## 🎉 安装完成

恭喜！您已经成功安装了Keil uVision5 MDK。
现在可以继续创建STM32贪吃蛇项目了。

## 📞 技术支持

如果遇到问题，可以：
1. 查看Keil官方文档
2. 访问Keil社区论坛
3. 联系技术支持
