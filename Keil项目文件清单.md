# STM32F411CEU6贪吃蛇游戏 - Keil项目文件清单

## 📁 项目文件结构

请按照以下结构创建您的Keil项目文件夹：

```
STM32_Snake_Keil/
├── Project/                    (Keil项目文件)
│   └── Snake_Game.uvprojx     (Keil项目文件，稍后创建)
├── Source/                     (源代码文件)
│   ├── main.c
│   ├── lcd_driver.c
│   ├── snake_game.c
│   ├── stm32f4xx_it.c
│   ├── system_stm32f4xx.c
│   └── startup_stm32f411xe.s
├── Include/                    (头文件)
│   ├── main.h
│   ├── lcd_driver.h
│   ├── snake_game.h
│   ├── stm32f4xx_it.h
│   └── stm32f4xx_hal_conf.h
└── Output/                     (编译输出)
    └── (编译后生成的文件)
```

## 📋 需要的源代码文件

### ✅ 核心源代码文件 (放入Source文件夹)
1. **main.c** - 主程序文件
2. **lcd_driver.c** - LCD显示屏驱动
3. **snake_game.c** - 贪吃蛇游戏逻辑
4. **stm32f4xx_it.c** - 中断服务程序
5. **system_stm32f4xx.c** - 系统初始化
6. **startup_stm32f411xe.s** - 启动文件（汇编）

### ✅ 头文件 (放入Include文件夹)
1. **main.h** - 主程序头文件
2. **lcd_driver.h** - LCD驱动头文件
3. **snake_game.h** - 游戏逻辑头文件
4. **stm32f4xx_it.h** - 中断服务程序头文件
5. **stm32f4xx_hal_conf.h** - HAL库配置文件

## 📥 文件获取方式

### 方法一：从当前目录复制（推荐）
当前目录中已经包含了所有需要的文件：

**源代码文件：**
- main.c ✅
- lcd_driver.c ✅
- snake_game.c ✅
- stm32f4xx_it.c ✅
- system_stm32f4xx.c ✅
- startup_stm32f411xe.s ✅

**头文件：**
- main.h ✅
- lcd_driver.h ✅
- snake_game.h ✅
- stm32f4xx_it.h ✅
- stm32f4xx_hal_conf.h ✅

### 方法二：手动创建文件夹并复制
1. 在桌面创建文件夹 `STM32_Snake_Keil`
2. 在其中创建子文件夹：`Project`, `Source`, `Include`, `Output`
3. 将对应文件复制到相应文件夹

## 🔧 Keil项目创建步骤

### 第一步：启动Keil uVision5
1. 双击桌面上的Keil uVision5图标
2. 等待软件完全加载

### 第二步：创建新项目
1. 菜单：**Project** → **New uVision Project...**
2. 浏览到 `STM32_Snake_Keil/Project/` 文件夹
3. 输入项目名称：`Snake_Game`
4. 点击 **"保存"**

### 第三步：选择目标设备
1. 在设备选择对话框中：
2. 展开 **"STMicroelectronics"**
3. 展开 **"STM32F4 Series"**
4. 展开 **"STM32F411"**
5. 选择 **"STM32F411CEUx"**
6. 点击 **"OK"**

### 第四步：配置运行时环境
在 "Manage Run-Time Environment" 对话框中：
1. **CMSIS** → 勾选 **"CORE"**
2. **Device** → 勾选 **"Startup"**
3. 点击 **"OK"**

### 第五步：添加源文件到项目
1. 在项目窗口中右键点击 "Target 1"
2. 选择 **"Add Group..."**，创建以下组：
   - `Application` (应用程序代码)
   - `Drivers` (驱动程序代码)
   - `System` (系统代码)

3. 为每个组添加对应的源文件：

**Application组：**
- 右键 → "Add Existing Files to Group 'Application'..."
- 选择：`main.c`, `snake_game.c`

**Drivers组：**
- 右键 → "Add Existing Files to Group 'Drivers'..."
- 选择：`lcd_driver.c`

**System组：**
- 右键 → "Add Existing Files to Group 'System'..."
- 选择：`stm32f4xx_it.c`, `system_stm32f4xx.c`

## ⚙️ 项目配置

### 第六步：配置项目选项
1. 右键项目名称 → **"Options for Target 'Target 1'..."**
2. 或按快捷键 **Alt+F7**

### Target选项卡配置：
- **Xtal (MHz)**：`16.0` (内部时钟)
- **Use On-chip ROM**：勾选
  - Size: `0x80000` (512KB)
  - Start: `0x8000000`
- **Use On-chip RAM**：勾选
  - Size: `0x20000` (128KB)
  - Start: `0x20000000`

### Output选项卡配置：
- **Name of Executable**：`Snake_Game`
- **Select Folder for Objects**：`../Output/`
- 勾选 **"Create HEX File"** ✅

### C/C++选项卡配置：
**Include Paths**：添加
```
../Include
../Source
```

**Define**：添加
```
USE_HAL_DRIVER
STM32F411xE
```

## 🔨 编译项目

### 第七步：编译
1. 点击工具栏的 **"Build"** 按钮 (🔨)
2. 或按快捷键 **F7**
3. 查看编译输出

### 成功编译的输出示例：
```
Build started: Project: Snake_Game
*** Using Compiler 'V6.16'
Build target 'Target 1'
compiling main.c...
compiling lcd_driver.c...
compiling snake_game.c...
compiling stm32f4xx_it.c...
compiling system_stm32f4xx.c...
assembling startup_stm32f411xe.s...
linking...
Program Size: Code=15234 RO-data=1456 RW-data=128 ZI-data=2048
"Snake_Game.axf" - 0 Error(s), 0 Warning(s).
Build Time Elapsed:  00:00:03
```

## 📁 编译输出文件

编译成功后，在 `Output/` 文件夹中会生成：
- **Snake_Game.hex** - HEX烧录文件 (主要文件)
- **Snake_Game.axf** - ELF可执行文件
- **Snake_Game.map** - 内存映射文件

## 🎉 完成！

如果看到 "0 Error(s), 0 Warning(s)" 的输出，恭喜您！
您已经成功编译了STM32贪吃蛇游戏项目。

**生成的HEX文件位置**：`STM32_Snake_Keil/Output/Snake_Game.hex`

这个文件就可以直接烧录到STM32F411CEU6单片机了！
